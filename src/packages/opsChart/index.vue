/*
 * @Author: wuchao wenhao.wang
 * @Date: 2024-04-30 19:34:27 
 * @Last Modified by: wenh<PERSON>.wang
 * @Last Modified time: 2024-10-28 18:08:30
 */
<template>
  <el-card class="chart-wapper" shadow="never">
    <div ref="chartDom" :style="{ width, height }"></div>
  </el-card>
</template>

<script lang="ts">
import type { ECharts } from 'echarts'
import { useDebounceFn } from '@vueuse/core'
import echarts from './index'
export default defineComponent({
  name: 'OpsChart',
})
</script>
<script lang="ts" setup>
const props = defineProps({
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '100%',
  },
  options: {
    type: Object,
    default: null,
  },
})
const chartDom = ref(null)
let chartObj: null | ECharts = null
let observer: null | ResizeObserver = null

onMounted(() => {
  if (!chartDom.value) return
  init()
  drawOption()
  observer = new ResizeObserver(entries => {
    debounceFn()
  })
  const debounceFn = useDebounceFn(() => {
    resize()
  }, 500)
  observer.observe(chartDom.value)
})

onUnmounted(() => {
  if (chartObj) {
    chartObj.dispose()
    chartObj = null
  }
  observer?.disconnect()
})

watch(() => props.options, () => drawOption())

// 初始化
const init = () => {
  chartObj = (echarts.init(chartDom.value) as any)
}
// 绘制方法
const drawOption = (options = props.options) => {
  if(!chartObj) return  
  if(JSON.stringify(options) === '{}') {
    chartObj.clear()
    chartObj.showLoading({
      text: '图表加载中',
      color: '#409eff',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, .95)',
      zlevel: 0,
      lineWidth: 2,
    })
  }
  else {
    chartObj.hideLoading()
    chartObj.setOption({
      ...options
    })
  }
}
// 重绘 自适应尺寸
const resize = () => {
  chartObj?.resize()
}
</script>

<style lang="scss" scoped>
.chart-wapper {
  .dis-flex {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    .ops-el-table {
      width: 100%;
      flex-grow: 1;
    }
  }
}
</style>
