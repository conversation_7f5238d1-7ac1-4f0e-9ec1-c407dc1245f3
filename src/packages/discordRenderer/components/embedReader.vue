/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2024-10-28 18:13:25 
 * @Last Modified by:   wenhao.wang 
 * @Last Modified time: 2024-10-28 18:13:25 
 */
<template>
  <div class="embed-box">
    <el-image v-if="props.embedData.type === 'gifv' || props.embedData.type === 'image'" :preview-teleported="true"
      hide-on-click-modal :src="gifUrl" class="embed-img" :preview-src-list="[gifUrl]" />
    <div v-if="props.embedData.type === 'video'" class="embed-video-wrap">
      <el-card class="link-video-preview" v-if="props.embedData.provider?.name === 'YouTube'">
        <div class="link-video-url">{{ props.embedData.url }}</div>
        <div class="link-video-title">
          <a :href="props.embedData.url" target="_blank">{{ props.embedData.title }}</a>
        </div>
        <iframe allow="autoplay" frameborder="0" scrolling="no"
          sandbox="allow-forms allow-modals allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts"
          :src="props.embedData.video?.url" :provider="props.embedData.provider.name" allowfullscreen=""></iframe>
      </el-card>
      <opsVideo v-else :video-url="props.embedData.url" :id="props.embedData.url" class="video-player-box" />
    </div>
    <div v-if="props.embedData.type === 'link' || props.embedData.type === 'article'" class="embed-link-wrap">
      <a :href="props.embedData.url" target="_blank">{{ props.embedData.url }}</a>
      <el-card class="link-preview">
        <div class="link-preview-title">
          <a :href="props.embedData.url" target="_blank">{{ props.embedData.title }}</a>
        </div>
        <div class="link-preview-description">{{ props.embedData.description }}</div>
        <!-- 文章类型可能会带图片 -->
        <el-image v-if="props.embedData.thumbnail && props.embedData.thumbnail.proxy_url" :src="props.embedData.thumbnail.proxy_url" class="art_img"/>
      </el-card>
    </div>
  </div>

</template>

<script lang="ts">
export default defineComponent({
  name: 'EmbedReader'
})
interface EmbedData {
  color: number // 颜色
  description: string // 描述
  title: string // 标题
  type: 'rich' | 'image' | 'video' | 'gifv' | 'article' | 'link' // 类型 "rich", "image", "video", "gifv", "article", "link"
  url: string // url
  provider?: {
    name: string // 提供者名称
    url: string // 提供者url
  }
  video?: {
    url: string // 视频url
    width: number // 宽度
    height: number // 高度
  }
  thumbnail?: {
    proxy_url: string // 代理url
    url: string // 缩略图url
    width: number // 宽度
    height: number // 高度
  }
}
interface EmbedReaderProps {
  embedData: EmbedData
}
</script>
<script setup lang="ts">
const props = defineProps<EmbedReaderProps>()
const gifUrl = ref<string>('')
if (props.embedData.type === 'gifv' || props.embedData.type === 'image') {
  if (props.embedData.provider && props.embedData.provider.name === 'Tenor') {
    fetch(`https://tenor.googleapis.com/v2/posts?ids=${props.embedData.url.split('-').pop()}&key=AIzaSyACChaCEB2Kig5tueZty_rRqmnffqqLUJc`)
      .then(response => response.json())
      .then(data => {
        gifUrl.value = data.results[0].media_formats.gif.url
      })
      .catch(error => {
        console.error('Failed to fetch GIF from Tenor:', error)
      })
  } else {
    gifUrl.value = props.embedData.thumbnail?.proxy_url || props.embedData.url
  }
}
</script>

<style lang="scss" scoped>
.embed-box {
  margin-bottom: 5px;
  margin-top: 5px;
}

.embed-img,
.embed-video-wrap {
  max-width: 350px;
  display: block;
  border-radius: 8px;
  overflow: hidden;
}
.art_img {
  border-radius: 8px;
  margin-top: 10px;
  
  // &::v-deep(.el-image__inner) {
  //   max-height: 260px;
  //   width: auto !important;
  // }
}

.embed-video-wrap {
  width: 100%;
  min-width: 200px;
  .link-video-preview {
    width: 100%;
    margin-top: 10px;
    &:deep(.el-card__body) {
      padding: 15px;
    }
    .link-video-url {
      font-size: 14px;
      margin-bottom: 16px;
    }
    .link-video-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 10px;
      a {
        color: color-mix(in oklab, hsl(212 calc(1 * 100%) 45.3% / 1) 100%, black 0%);
        text-decoration: none;
      }
    }
  }
}
.embed-link-wrap {
  word-wrap: break-word;
  a {
    color: color-mix(in oklab, hsl(212 calc(1 * 100%) 45.3% / 1) 100%, black 0%);
    text-decoration: underline;
    display: block;
    word-break: break-all;
  }
  .link-preview {
    width: 100%;
    margin-top: 10px;
    &:deep(.el-card__body) {
      padding: 15px;
    }
    .link-preview-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 5px;
      a {
        color: color-mix(in oklab, hsl(212 calc(1 * 100%) 45.3% / 1) 100%, black 0%);
        text-decoration: none;
      }
    }
  }
}
</style>