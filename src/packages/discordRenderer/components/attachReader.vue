/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2024-10-28 18:13:15 
 * @Last Modified by:   wenhao.wang 
 * @Last Modified time: 2024-10-28 18:13:15 
 */
<template>
  <div class="attach-box">
    <el-image v-if="props.attachData.content_type.includes('image')" :preview-teleported="true" hide-on-click-modal
      :src="props.attachData.url" class="attach-img" :preview-src-list="[props.attachData.url]" />
    <div v-else-if="props.attachData.content_type.includes('video')" class="attach-video-wrap">
      <opsVideo :video-url="props.attachData.url" :id="props.attachData.id" class="video-player-box" />
    </div>
    <div v-else-if="props.attachData.content_type.includes('audio')" class="attach-audio-wrap">
      <audio controls controlslist="noplaybackrate nodownload">
        <source :src="props.attachData.url" :type="props.attachData.content_type === 'audio' ? 'audio/mp4' : props.attachData.content_type">
        Your browser does not support the audio element.
      </audio>
    </div>
    <div v-else class="else-attach">
      <div>{{ $t('text_cant_read') }}</div>
      <a :href="props.attachData.url">{{ props.attachData.filename || props.attachData.content_type }}</a>
    </div>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: 'AttachReader'
})
interface AttachData {
  content_type: string // 内容类型
  ephemeral?: boolean // 是否临时
  filename?: string // 文件名
  height?: number // 高度
  id: string // id
  proxy_url?: string // 代理url
  size?: number // 大小
  url: string // url
  width?: number // 宽度
}
interface AttachReaderProps {
  attachData: AttachData
}
</script>
<script setup lang="ts">
const props = defineProps<AttachReaderProps>()
</script>

<style lang="scss" scoped>
.attach-box {
  margin: 10px auto 5px;
}
.attach-img, .attach-video-wrap {
  max-width: 350px;
  display: block;
  border-radius: 8px;
  overflow: hidden;
}
.attach-video-wrap {
  width: 100%;
  min-width: 200px;
}
.else-attach {
  a {
    margin: 5px 0 0;
    color: color-mix(in oklab, hsl(212 calc(1 * 100%) 45.3% / 1) 100%, black 0%);
    text-decoration: underline;
    display: block;
  }
}
</style>