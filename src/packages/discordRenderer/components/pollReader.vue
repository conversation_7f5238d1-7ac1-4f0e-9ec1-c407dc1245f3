/*
 * @Author: we<PERSON><PERSON>.wang 
 * @Date: 2024-10-28 18:13:43 
 * @Last Modified by:   wenhao.wang 
 * @Last Modified time: 2024-10-28 18:13:43 
 */
<template>
  <div style="margin-top: 10px;">
    <div class="poll-title">{{ props.pollData.question_text }}</div>
    <div class="tips">{{ props.pollData.allow_multiselect ? '请选择一个或多个选项' : '请选择一个选项' }}</div>
    <el-checkbox-group v-model="pollResults" size="small" v-if="props.pollData.allow_multiselect" disabled>
      <el-checkbox class="poll-item" v-for="(item, key) in props.pollData.answers" :key="key" :label="item.answer_text"
        :value="item.answer_id" border />
    </el-checkbox-group>
    <el-radio-group v-model="pollResults" size="small" v-else disabled>
      <el-radio class="poll-item" v-for="(item, key) in props.pollData.answers" :key="key" :label="item.answer_text"
        :value="item.answer_id" border>Option A</el-radio>
    </el-radio-group>
    <div class="t">
      <Warning />暂只支持展示投票问题和选项
    </div>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: 'pollReader'
})
interface PollReaderProps {
  pollData: any
}
</script>
<script setup lang="ts">
const props = defineProps<PollReaderProps>()
const pollResults = ref<any>()
</script>

<style lang="scss" scoped>
.poll-title {
  font-size: 18px;
  font-weight: bold;
  margin: 8px auto 5px;
}
.tips {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}
.poll-item {
  display: flex;
  justify-content: left;
  align-items: center;
  line-height: 1;
  width: 100%;
  border-color: #a5d0c0 !important;
  margin: 5px auto;
  font-size: 12px;
  color: #4aa181 !important;
  background-color: #edf6f2;
  border-radius: 6px !important;
  &:deep(span) {
    color: #4aa181 !important;
  }
}
.t {
  color: #999;
  font-size: 10px;
  margin-top: 10px;
  display: flex;
  justify-content: left;
  align-items: center;
  line-height: 1;
  svg {
    vertical-align: middle;
    margin: 0 2px 0px 0px;
    width: 1.1em;
    height: 1.1em;
  }
}
</style>