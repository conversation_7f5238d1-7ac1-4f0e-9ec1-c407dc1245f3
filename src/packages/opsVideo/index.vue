/*
 * @Author: chao.wu
 * @Date: 2024-05-10 11:51:01 
 * @Last Modified by: wenh<PERSON>.wang
 * @Last Modified time: 2024-10-15 12:11:12
 */
<template>
  <div :width='props.width' :height='props.height' :id='id'></div>
  <el-button link icon="Download" style="margin-top: 5px;" @click="downloadVideo">点击下载</el-button>
</template>

<script setup lang='ts'>
import { onMounted } from 'vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'

/**
* @author: chao.wu
* @description: 文档：https://h5player.bytedance.com/ 源码：https://github.com/bytedance/xgplayer
* @return {*} 支持视频图片封面 + UI定制化
*/

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  videoUrl: {
    type: String,
    default: () => ''
  },
  playsinline: {
    type: <PERSON><PERSON><PERSON>,
    default: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  }
})

// 定义一个变量来存储 player 实例
let player: Player
onMounted(() => {
  if (!player) initPlayer()
})

// 初始化西瓜视频播放器
const initPlayer = () => {
  player = new Player({
    lang: 'zh',
    volume: 0.3, // 默认音量
    id: props.id,
    url: props.videoUrl,
    height: props.height,
    width: props.width,
    fitVideoSize: 'fixed', // 自适应播放器大小
    playsinline: props.playsinline, // 是否全屏播放
    // poster: props.poster, // 图片封面
    // plugins: [Mp4Plugin], // 插件
    // closeVideoClick: true, // 单击暂停/播放
    // closeVideoDblclick: true, // 双击全屏
    cssFullscreen: false, // 显示样式全屏
    download: false, // 显示下载按钮
    // controls: false,
    // marginControls: true,
    controls: {
      mode: 'bottom'
    },
    // icons: {
    //   startPlay: `<div></div>`,
    //   startPause: `<div></div>`
    // },
    // ignores: ['start', 'progresspreview'],
    commonStyle: {
      // 进度条底色
      progressColor: '',
      // 播放完成部分进度条底色
      playedColor: '#4aa181',
      // 缓存部分进度条底色
      cachedColor: '',
      // 进度条滑块样式
      sliderBtnStyle: {},
      // 音量颜色
      volumeColor: '#4aa181'
    },
    playbackRate: [0.25, 0.5, 1, 1.5, 2, 3],
    // inactive: 1500, //播放器focus状态自动消失延迟时长，单位为ms
    // leavePlayerTime: 1500, //鼠标移出播放器区域就隐藏时间
    autoplay: false,
    whitelist: ['']
  })
  // player.play()
}

// 下载视频
const downloadVideo = () => {
  const a = document.createElement('a')
  a.href = props.videoUrl
  a.download = 'video'
  a.click()
}
</script>
