/*
 * @Author: wenh<PERSON>.wang
 * @Date: 2024-05-14 16:22:10
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-12-23 20:11:29
 */

<template>
  <div class="edit-container">
    <QuillEditor @blur="editorBlur" theme="snow" contentType="html" class="ql-editor" v-model:content="content"
      ref="myQuillEditorRef" :options="editorOption">
    </QuillEditor>
    <div class="demo-image__preview">
      <!-- 图片预览 -->
      <el-image-viewer hide-on-click-modal @close="()=>{showViewer = false}" v-if="showViewer" :url-list="previewList" />
    </div>
  </div>
</template>

<script lang="ts">
import { QuillEditor, Quill } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import 'quill-image-uploader/dist/quill.imageUploader.min.css'
import ImageUploader from 'quill-image-uploader'
import { uploadImage } from '@/api/common'
import { useI18n } from 'vue-i18n'
import { useUserInfoStore } from '@/stores'
import { nextTick } from 'vue'
Quill.register("modules/imageUploader", ImageUploader)
export default defineComponent({
  name: 'OpsEditer',
  components: {
    ElMessage,
    QuillEditor
  }
})
interface EditerProps {
  modelValue: string
}
</script>
<script lang="ts" setup>
const { t: $t } = useI18n()
const userTocken = computed(() => useUserInfoStore().userTocken as string)
const props = withDefaults(defineProps<EditerProps>(), {
  modelValue: ''
})
const emit = defineEmits<{
  (event: 'update:modelValue', value: string): void
  (event: 'update:uploading', value: boolean): void
}>()
const imgUploading = ref(false)
const showViewer = ref<boolean>(false)
const previewList = ref<string[]>([])
const content = computed({
  get: () => props.modelValue,
  set: (val: string) => {
    if (val.indexOf('data:image/') > -1) {
      val = val.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/g, '')
      if (imgUploading) return
      ElMessage.error('您粘贴的内容中包含base64图片，数据库不允许储存base64图片，请通过上传图片按钮重新上传图片！')
    }
    if (isEmptyQuillContent(val)) {
      val = ''
    }
    emit('update:modelValue', val)
  }
})

const isEmptyQuillContent = (html: string) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, "text/html")
  const text = doc.body.textContent || ""
  const images = doc.getElementsByTagName('img')
  let hasImageContent = false

  for (let i = 0; i < images.length; i++) {
    if (images[i].src.trim() !== '') {
      hasImageContent = true
      break
    }
  }

  return text === "" && !hasImageContent
}

const myQuillEditorRef = ref()
const editorOption = {
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'align': [] }],
      ['clean'],
      // [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      // [{ 'indent': '-1' }, { 'indent': '+1' }],
      ['image', 'link'],

      // [{ 'color': [] }, { 'background': [] }]
    ],
    imageUploader: {
      upload: (file: any) => {
        return uploadImageHandle(file)
      }
    },
    clipboard: {
      matchVisual: false
    }
  },
  placeholder: $t('place_input') + '...'
}

// 初始化QuillEditor粘贴事件处理
const onEditorReady = (quill: any) => {
  // 添加自定义粘贴处理
  quill.root.addEventListener('paste', function(e: ClipboardEvent) {
    // 阻止默认粘贴行为
    e.preventDefault();

    const clipboardData = e.clipboardData;
    if (!clipboardData) return;

    // 获取纯文本，确保去除所有格式
    const text = clipboardData.getData('text/plain') || '';

    if (text) {
      // 获取当前选区
      const range = quill.getSelection(true);
      const cursorPosition = range ? range.index : quill.getLength();

      // 在当前位置插入纯文本（不传入格式参数）
      quill.insertText(cursorPosition, text);

      // 使用延时确保光标更新在DOM渲染后执行
      const timeout = setTimeout(() => {
        const newPosition = cursorPosition + text.length;
        console.log('设置光标位置:', newPosition);
        quill.setSelection(newPosition, 0);
        clearTimeout(timeout);
      }, 10);
    }
  });
}

const uploadImageHandle = async (file: any) => {
  imgUploading.value = true
  emit('update:uploading', true)
  const formdata = new FormData()
  formdata.append('file', file)
  let url
  try {
    const res = await uploadImage(formdata as any)
    url = res.url
  } catch (error) {
    ElMessage.error(error as string)
  } finally {
    imgUploading.value = false
    emit('update:uploading', false)
  }
  return url
}

// 修复quill编辑器失焦后位置偏移问题
const editorBlur = (element: any) => {
  let e: HTMLElement | null = element._value.querySelector(".ql-tooltip,.ql-editing")
  if (e) {
    let left = e.style.borderLeftStyle
    if (left.indexOf("-") === 0 || left === "") {
      e.style.left = "5px"
    }
  }
}

// const resizeHandle = (e: HTMLElement) => {
//   if (e.querySelector('.ql-toolbar')) {
//     const toolbarHeight = (e.querySelector('.ql-toolbar') as HTMLElement).offsetHeight
//     const container = e.querySelector('.ql-editor.ql-container.ql-snow') as HTMLElement
//     console.log(toolbarHeight, container)
//     container.style.setProperty('--toolbar-height', toolbarHeight + 'px')
//   }
// }

// 修复quill编辑器高度问题
let resizeObserver: ResizeObserver | null = null
onMounted(() => {
  // 不再需要调用initPasteHandler

  resizeObserver = new ResizeObserver(function(entries) {
    for (let entry of entries) {
      if (entry.target.classList.contains('ql-toolbar')) {
        const toolbarHeight = (entry.target as HTMLElement).offsetHeight
        const container = document.querySelector('.ql-editor.ql-container.ql-snow') as HTMLElement
        container.style.setProperty('--toolbar-height', toolbarHeight + 'px')
      }
    }
  })
  const toolbar = document.querySelector('.ql-toolbar')
  resizeObserver.observe(toolbar as Element)
  // 监听图片点击事件
  document.querySelector('.ql-editor')?.addEventListener('click', ($event: any) => {
    let target = $event.target as HTMLImageElement | null
    if (target && target instanceof HTMLImageElement && target.tagName === 'IMG' && target.src) {
      previewList.value = [target.src]
      showViewer.value = true
    }
  })
})
onBeforeUnmount(() => {
 if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>
<style lang="scss" scoped>
.edit-container {
  height: 100%;
  width: 100%;

  &::v-deep(.ql-toolbar.ql-snow + .ql-container.ql-snow) {
    height: calc(100% - var(--toolbar-height));
    min-height: 150px;
  }
  .resize-handle {
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
    width: 0;
    height: 0;
    padding: 0px;
    margin: 0px;
    border-left: 10px solid transparent;
    border-top: 10px solid transparent;
    border-bottom: 10px solid lightgray;
    border-right: 10px solid lightgray;
  }
}
</style>
