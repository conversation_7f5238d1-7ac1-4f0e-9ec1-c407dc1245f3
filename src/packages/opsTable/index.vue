/*
 * @Author: we<PERSON><PERSON>.wang 
 * @Date: 2024-04-30 19:33:41 
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-09-24 18:13:03
 */
<template>
  <el-card class="table-wapper" shadow="never">
    <template #header v-if="hasHeader">
      <slot name="header"></slot>
    </template>
    <div class="dis-flex">
      <el-table ref="opsTable" :data="tableData.list" v-loading="loading" :key="resetKey + _uid" :border="props.border" stripe :size="props.size"
        :header-cell-style="{ background: '#fafafa', color: '#606266' }" class="ops-el-table">
        <slot></slot>
      </el-table>
    </div>
    <template #footer v-if="!!tableData.total">
      <el-pagination :size="props.size" class="pagination" v-model:current-page="tableData.page" :page-sizes="[20, 50, 100, 150, 200]"
        v-model:page-size="tableData.pageSize" :total="tableData.total" layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handleCurrentChange">
      </el-pagination>
    </template>
  </el-card>
</template>

<script lang="ts">
import { v4 as uuidv4 } from 'uuid'
export default defineComponent({
  name: 'OpsTable'
})
interface OpsTableProps {
  dataApi: (params: any) => Promise<TableResponse>
  params?: any
  isInit?: boolean
  border?: boolean
  size?: '' | 'large' | 'default' | 'small'
}
</script>
<script setup lang="ts">
const _uid = uuidv4()
const slots = useSlots()
const opsTable = ref()
const hasHeader = computed(() => !!slots.header)

const props = withDefaults(defineProps<OpsTableProps>(), {
  params: {},
  isInit: true,
  border: false,
  size: 'default'
})
const resetKey = ref(0)
const loading = ref(false)
const tableData = reactive({
  list: [] as Record<string, unknown>[], // 表格数据
  total: 0, // 总数
  page: 1, // 当前页
  pageSize: 20, // 每页条数
})
// let { page, pageSize, total, list } = toRefs(tableData)
const reqParams = computed(() => {
  return {
    ...props.params,
    page: Number(tableData.page),
    page_size: Number(tableData.pageSize)
  }
})
// 刷新表格方法
const refreshTable = () => {
  resetKey.value = Math.random()
}
// 获取数据方法
const getData = () => {
  if (!props.dataApi) return console.error('未读取到接口API！')
  loading.value = true
  props.dataApi(reqParams.value).then((res: TableResponse) => {
    tableData.list = ('records' in res.data) ? res.data.records : res.data
    tableData.page = res.current_page
    tableData.total = res.total
  }).finally(() => {
    loading.value = false
  })
}
const handleSizeChange = (val: number) => {
  tableData.page = 1
  tableData.pageSize = val
  getData()
}
const handleCurrentChange = (val: number) => {
  tableData.page = val
  getData()
}


if ( props.isInit ) {
  getData()
}
defineExpose({
  getData,
  refreshTable
})
</script>

<style lang="scss" scoped>
.table-wapper {
  .dis-flex {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    .ops-el-table {
      width: 100%;
      flex-grow: 1;
    }
  }
  .pagination {
    float: right;
  }
}
</style>