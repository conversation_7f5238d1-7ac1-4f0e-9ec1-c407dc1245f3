/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2024-11-01 16:08:48 
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-11-07 20:09:25
 */
<template>
  <el-input
    v-model="displayValue"
    @clear="handleClear"
    :placeholder="placeholder"
    :clearable="hasClearable"
  />
</template>

<script setup lang="ts">

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  placeholder: {
    type: String,
    default: '',
  },
  clearable: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue'])

const displayValue = computed({
  get: () => props.modelValue === 0 ? '' : String(props.modelValue),
  set: (newValue: string) => {
    if (isNaN(Number(newValue))) {
      return
    }
    const numericValue = newValue === '' ? 0 : Number(newValue)
    emit('update:modelValue', numericValue)
  }
})

const handleClear = () => {
  emit('update:modelValue', 0)
}

const hasClearable = computed(() => props.clearable)
</script>