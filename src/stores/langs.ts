/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2024-04-13 18:19:18 
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-04-30 10:36:28
 */

import { defineStore } from "pinia"
import { ref } from 'vue'
import i18n from '@/plugins/i18n'
import moment from 'moment'

export const useLangsStore = defineStore('langs', () => {
  let locale = ref(i18n.global.locale.value)
  function setLocale (lang: string) {
    moment.locale(lang === 'zh' ? 'zh-cn' : 'en')
    locale.value = lang as "zh" | "en"
    i18n.global.locale.value = lang as "zh" | "en"
  }
  return { locale, setLocale }
}, {
  persist: true
})