<template>
  <div class="ops-sider" :class="collapseMark ? 'is-close' : 'is-open'">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :collapse="collapseMark" :collapse-transition="false" :unique-opened="true"
        :default-active="$route.path">
        <template v-for="(item, index) in routes" :key="index">
          <el-sub-menu v-if="item.children && item.children.length > 1" :index="item.path">
            <template #title v-if="item.meta && !item.meta.hidden">
              <el-icon>
                <component :is="item.meta.icon" />
              </el-icon>
              <span>{{ $t(item.meta.title as string) }}</span>
            </template>
            <template v-if="item.meta && !item.meta.hidden" v-for="(itemChild, indexChild) in item.children"
              :key="`child_${indexChild}`">
              <router-link :to="itemChild.path">
                <el-menu-item :index="itemChild.path" v-if="itemChild.meta && itemChild.meta && !itemChild.meta.hidden">
                  <template #title>
                    <el-icon>
                      <component :is="itemChild.meta.icon" />
                    </el-icon>
                    <span>{{ $t(itemChild.meta.title as string) }}</span>
                  </template>
                </el-menu-item>
              </router-link>
            </template>
          </el-sub-menu>
          <router-link v-if="item.children && item.children.length === 1" :to="item.children[0].path">
            <el-menu-item v-if="item.children[0].meta && !item.children[0].meta.hidden" :index="item.children[0].path">
              <el-icon>
                <component :is="item.children[0].meta.icon" />
              </el-icon>
              <template #title>
                <span>{{ $t(item.children[0].meta.title as string) }}</span>
              </template>
            </el-menu-item>
          </router-link>
          <router-link v-if="!item.children || item.children.length === 0" :to="item.path">
            <el-menu-item v-if="item.meta && !item.meta.hidden" :index="item.path">
              <el-icon>
                <component :is="item.meta.icon" />
              </el-icon>
              <template #title>
                <span>{{ $t(item.meta.title as string) }}</span>
              </template>
            </el-menu-item>
          </router-link>
        </template>
      </el-menu>
    </el-scrollbar>
    <div class="toggle-icon" @click="toggle">
      <el-icon>
        <Expand v-if="collapseMark" />
        <Fold v-else />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts">
import { useAppStore, useUserInfoStore } from '@/stores'
export default defineComponent({
  name: 'OpsHeader'
})
</script>
<script setup lang="ts">
const collapseMark = computed(() => useAppStore().collapseMark)
const routes = computed(() => {
  return useUserInfoStore().routes
})
const toggle = () => {
  useAppStore().toggleSideBar()
}
</script>

<style lang="scss" scoped>
.ops-sider {
  height: 100%;
  padding-bottom: 50px;
  position: relative;
  transition: width .28s;
  &.is-open {
      width: 230px;
    }
  &.is-close {
    width: 64px;
  }
  .toggle-icon {
    cursor: pointer;
    height: 50px;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    font-size: 20px;
    color: #ccc;
    text-align: center;
    line-height: 50px;
  }

  .toggle-icon:hover {
    color: #000;
    background: rgba(0, 0, 0, .025);
  }
  .el-scrollbar {
    height: 100%;
    .el-menu {
      width: 100%;
      border-right: none;
      .el-menu-item {
        background-color: transparent;
      }
    
      .el-menu-item:hover {
        color: #85c9b0;
      }
    }
    &:deep(.el-sub-menu){
      .el-menu{
        background-color: #f7f7f7;
        .el-menu-item{
          background-color: transparent;
        }
        .el-menu-item:hover {
          color: #85c9b0;
        }
      }
      
    }
  }
}
</style>