<template>
  <div>
    <el-breadcrumb class="ops-breadcrumb" separator-icon="ArrowRight">
      <el-breadcrumb-item v-for="(v, k) in breadcrumbData" :key="k" :to="v.path">{{ $t(v.name as
        string)}}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="times">UTC {{ UTCtime }}</div>
    <div class="online-box">
      <span class="user-name">{{ userInfo.username }}：</span>
      <el-switch :model-value="isLogin" :active-value="1" :inactive-value="2" inline-prompt
        :active-text="$t('text_online')" :inactive-text="$t('text_offline')" @change="onoffline" />
    </div>
    <el-popover v-model:visible="showQCList" :title="$t('text_notification_QC')" placement="left" :width="600" trigger="click">
      <template #reference>
        <el-badge :is-dot="redDotVisible" class="message-box">
          <el-button type="info" icon="Message" link @click="_table.getData()"/>
        </el-badge>
      </template>
      <ops-table class="QC-table" ref="_table" :data-api="getQualityMessageList" :params="{}" :size="'small'">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
          <template #default="scope">
            {{ scope.row[item.prop] || '--' }}
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('btn_op')"
          width="80"
          align="center"
        >
          <template #default="scope">
            <el-badge :is-dot="!scope.row.has_read" class="reddot">
              <el-button
                link
                type="primary"
                @click="detailHandle(scope.row)"
              >{{ $t('text_views') }}</el-button>
            </el-badge>
          </template>
        </el-table-column>
      </ops-table>
    </el-popover>
    
  </div>

</template>

<script lang="ts">
import { useUserInfoStore } from '@/stores'
import { UTCtimer } from '@/utils/time'
import { useI18n } from 'vue-i18n'
import { online, getQualityMessageList, getQualityMessageRedDot } from '@/api/common'
export default defineComponent({
  name: 'OpsNav'
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>)
const isLogin = computed(() => useUserInfoStore().isLogin)
const router = useRouter()
const breadcrumbData = computed(() => {
  const matched = router.currentRoute.value.matched
  return matched.map((item) => {
    return {
      path: item.path,
      name: item.meta.title
    }
  })
})

const showQCList = ref(false)
const _table = ref()
const columns = computed((): Column[] => {
  return [
    { prop: 'created_at', label: $t('text_notification_time') },
    { prop: 'to_user', label: $t('text_notification_per') },
    { prop: 'msg_group', label: $t('text_notification_type')}
  ]
})
const detailHandle = (row: Record<string, unknown>) => {
  showQCList.value = false
  if (row.task_group === 2) {
    router.push({
      name: 'DiscordQC',
      query: {
        id: row.detail_id as number,
        t: new Date().getTime()
      }
    })
  } else {
    router.push({
      name: 'TicketsQC',
      query: {
        id: row.detail_id as number,
        t: new Date().getTime()
      }
    })
  }
}

const redDotVisible = ref(false)
const getRedDot = async () => {
  try {
    const resp = await getQualityMessageRedDot({})
    redDotVisible.value = resp.new_notice
  } catch (error) {
    console.log(error)
  }
}
getRedDot()
const UTCtime = ref(UTCtimer())
const clockTimer = setInterval(() => {
  UTCtime.value = UTCtimer()
  if (new Date().getMinutes() % 5 === 0 && new Date().getSeconds() === 0) {
    getRedDot()
  }
}, 1000)
onUnmounted(() => {
  clearInterval(clockTimer)
})
const onoffline = async () => {
  try {
    const resp = await online({
      status: isLogin.value === 1 ? 2 : 1,
      account: userInfo.value.username
    })
    useUserInfoStore().setCsStatus(resp.is_login)
  } catch (error) {
    console.log(error)
  }
}
</script>

<style lang="scss" scoped>
.ops-breadcrumb {
  float: left;
  line-height:40px;
}
.online-box {
  float: right;
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  .user-name {
    margin-right: 2px;
  }
}
.times {
  font-size: 12px;
  float: right;
  color: #909399;
  margin: 15px 0px 0px 20px;
}
.message-box {
  float: right;
  margin-top: 5px;
  margin-right: 10px;
  .el-button {
    font-size: 24px !important;
  }
  &::v-deep(.el-badge__content.is-fixed.is-dot) {
    top: 7px;
    right: 9px;
  }
}
.reddot {
  &::v-deep(.el-badge__content.is-fixed.is-dot) {
    top: 5px;
    right: 5px;
  }
}
.QC-table {
  &::v-deep(.el-card__body) {
    padding: 0px;
  }
  &::v-deep(.el-card__footer) {
    overflow: hidden;
    padding: 0 10px 0 0;
    .pagination {
      transform: scale(0.75);
      transform-origin: right center;
    }
  }
}
</style>