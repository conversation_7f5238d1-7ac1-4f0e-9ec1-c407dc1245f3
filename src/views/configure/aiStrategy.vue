<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
         <!-- 游戏 -->
        <el-form-item :label="`${$t('text_game')}：`">
          <el-select v-model="searchForm.project" :placeholder="$t('place_select')" clearable filterable multiple style="width: 180px">
            <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
              :value="v.game_project"></el-option>
          </el-select>
        </el-form-item>
        <!-- 策略名称 -->
        <el-form-item :label="`${$t('text_strategy_name')}：`">
          <el-input v-model="searchForm.strategy_name" :placeholder="$t('text_strategy_name_placeholder')" style="width: 180px" clearable></el-input>
        </el-form-item>
        <!-- 操作 -->
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="default" icon="RefreshRight" plain @click="resetSearchHandle">{{ $t('btn_reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="button-group">
      <el-button size="small" type="primary" icon="Plus" plain @click="addStrategyHandle">{{ $t('text_new_add') }}</el-button>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="strategyList" :params="params" v-loading="loading">
        <el-table-column prop="strategy_id" :label="$t('text_strategy_id')" width="100" align="center"></el-table-column>
        <el-table-column prop="strategy_name" :label="$t('text_strategy_name')" min-width="180" align="center"></el-table-column>
        <el-table-column prop="project" :label="$t('project')" width="150" align="center">
          <template #default="scope">
            <span>{{ gameList[scope.row.project]?.app_name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('text_strategy_status')" width="120" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.enable"
               active-color="#13ce66"
              inline-prompt
              inactive-color="#ff4949"
              :active-text="$t('text_enable')"
              :inactive-text="$t('text_disable')"
              :active-value="1"
              :inactive-value="2"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column :label="$t('btn_op')" align="center" width="200">
          <template #default="scope">
            <el-button size="small" type="primary" plain @click="editStrategyHandle(scope.row)">{{ $t('btn_edit') }}</el-button>
            <el-popconfirm :title="$t('text_strategy_delete_confirm')" @confirm="deleteStrategyHandle(scope.row)">
              <template #reference>
                <el-button size="small" type="danger" plain>{{ $t('text_delete') }}</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <add-strategy v-if="addStrategyVisible" v-model:visible="addStrategyVisible" :edit-data="editData" @success="searchHandle" />
  </div>
</template>

<script lang="ts">
// @ts-ignore
import { strategyList, strategyEnable, strategyDelete } from '@/api/strategy'
import { useI18n } from 'vue-i18n'
// @ts-ignore
import { useEnumStore } from '@/stores'
import { defineComponent } from 'vue'
import AddStrategy from './components/addStrategy.vue'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'AIStrategy',
  components: { AddStrategy }
})
</script>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'

const gameList = computed(() => useEnumStore().gameList)

const { t: $t } = useI18n()
const _table = ref()

const searchForm = ref({
  project: [],
  strategy_name: '',
  page: 1,
  page_size: 10
})

const loading = ref(false)

const params = computed(() => {
  return {
    ...searchForm.value,
    project: searchForm.value.project
  }
})

const searchHandle = () => {
  searchForm.value.page = 1
  _table.value.getData()
}

const resetSearchHandle = () => {
  searchForm.value = {
    project: [],
    strategy_name: '',
    page: 1,
    page_size: 10
  }
  // 确保params计算属性更新
  nextTick(() => {
    searchHandle()
  })
}

// 添加策略相关
const addStrategyVisible = ref(false)
const editData = ref(null)

const addStrategyHandle = () => {
  editData.value = null
  addStrategyVisible.value = true
}

const editStrategyHandle = (row: any) => {
  editData.value = row
  addStrategyVisible.value = true
}

// 删除策略相关
const deleteStrategyHandle = async (row: any) => {
  loading.value = true
  try {
    await strategyDelete({ strategy_id: row.strategy_id })
    ElMessage.success($t('text_delete_success'))
    searchHandle()
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 状态变更相关
const handleStatusChange = async (row: any) => {
  loading.value = true
  try {
    await strategyEnable({
      strategy_id: row.strategy_id,
      enable: row.enable
    })
    ElMessage.success($t('text_operate_success'))
  } catch (error) {
    console.error(error)
    row.enable = row.enable === 2 ? 1 : 2 // 恢复状态
    ElMessage.error($t('text_operate_failed'))
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.button-group {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 0 10px;
}
</style>
