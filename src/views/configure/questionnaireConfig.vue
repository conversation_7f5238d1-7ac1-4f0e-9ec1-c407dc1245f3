<template>
  <div class="page-view-wapper">
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="questionnaireConfigList" :params="searchHandle">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
          <template #default="scope">
            <template v-if="item.prop === 'projects'">
              <span v-for="(v, index) in scope.row[item.prop]" :key="index">{{ v }}{{ index === scope.row.projects.length - 1 ? '' : '、' }}</span>
            </template>
            <template v-else-if="item.prop === 'enable'">
              <el-switch
                v-model="scope.row[item.prop]"
                :active-value="true"
                :inactive-value="false"
                active-color="#13ce66"
                inline-prompt
                inactive-color="#ff4949"
                :active-text="$t('text_enable')"
                :inactive-text="$t('text_disable')"
                @change="switchEnable($event as boolean, scope.row)"
              >
              </el-switch>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('btn_op')"
          width="200"
          align="center"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="editHandle(scope.row)"
            >{{ $t('btn_edit') }}</el-button>
            <el-button
              link
              type="primary"
              :disabled="!scope.row.can_gen_link"
              @click="linkHandle(scope.row)"
            >{{ $t('text_btn_links') }}</el-button>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <editQuestion v-if="qEditVisible" v-model:visible="qEditVisible" :edit-data="questionData" @success="searchHandle" />
    <questionLink v-if="linkVisible" v-model:visible="linkVisible" :link-data="linkData" />
  </div>
</template>

<script lang="ts">
import { questionnaireConfigList, questionnaireConfigEnable } from '@/api/questionnaireConfig'
import { useI18n } from 'vue-i18n'
import editQuestion from './components/editQuestionConfig.vue'
import questionLink from './components/questionLink.vue'
export default defineComponent({
  name: 'QuestionnaireConfig',
  components: {
    ElMessage,
    editQuestion,
    questionLink
  }
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const _table = ref()
const columns = computed((): Column[] => {
  return [
    { prop: 'id', label: 'ID'},
    { prop: 'project', label: $t('text_game') },
    { prop: 'updated_at', label: $t('text_update_at') },
    { prop: 'op', label: $t('text_update_by')},
    { prop: 'enable', label: $t('text_status'), width: '100'}
  ]
})
const searchHandle = () => {
  _table.value.getData()
}

const qEditVisible = ref(false)
const questionData = ref({})
const linkVisible = ref(false)
const linkData = ref({})

const switchEnable = async (value: boolean, row: Record<string, unknown>) => {
  try {
    await questionnaireConfigEnable({
      object_id: row.id,
      enable: value
    })
    ElMessage.success($t('text_status_success'))
    searchHandle()
  } catch (error) {
    console.log(error)
    row.enable = !value
  }
}
const editHandle = (row: Record<string, unknown>) => {
  questionData.value = row
  qEditVisible.value = true
}
const linkHandle = (row: Record<string, unknown>) => {
  linkData.value = row
  linkVisible.value = true
}
</script>