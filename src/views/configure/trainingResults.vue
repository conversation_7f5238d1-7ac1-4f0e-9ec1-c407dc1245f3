<template>
  <div class="page-view-wapper">
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="getTrainingResultsList" :params="params">
        <el-table-column prop="created_at" :label="$t('text_training_time')" align="center">
          <template #default="scope">
            {{ scope.row.created_at || '--' }}
          </template>
        </el-table-column>
        <!-- 游戏 -->
        <el-table-column prop="game_project" :label="$t('text_game')" align="center">
          <template #default="scope">
            <span>{{ gameList[scope.row.project]?.app_name || '--' }}</span>
          </template>
        </el-table-column>
        <!-- 语言 -->
        <el-table-column prop="lang" :label="$t('text_lang')" align="center">
          <template #default="scope">
            <span>{{
              (langList && langList.find(v => v.code === scope.row.lang)?.name) || '--'
            }}</span>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column prop="operator" :label="$t('text_operator')" align="center">
          <template #default="scope">
            {{ scope.row.operator || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('text_result')" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">{{
              getStatusText(scope.row.status)
            }}</el-tag>
          </template>
        </el-table-column>
      </ops-table>
      <!-- 底部分页 -->
      <div class="pagination-container">
        <span class="total-records">共 {{ totalRecords }} 条记录</span>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalRecords"
          :current-page="currentPage"
          :page-size="pageSize"
          @current-change="handlePageChange"
          :pager-count="5"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-ignore
import { getTrainingResultsList } from '@/api/trainingResults';
import { useI18n } from 'vue-i18n';
// @ts-ignore
import { useEnumStore } from '@/stores';
import { defineComponent } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

export default defineComponent({
  name: 'TrainingResults',
});
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

const gameList = computed(() => useEnumStore().gameList);
const langList = computed(() => useEnumStore().LangsList);
console.log('gameList', gameList.value);
console.log('langList', langList.value);
const { t: $t } = useI18n();
const _table = ref();

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalRecords = ref(125);

const params = computed(() => {
  return {
    page: currentPage.value,
    page_size: pageSize.value,
  };
});

const columns = computed((): any[] => {
  return [
    { prop: 'created_at', label: $t('text_training_time') },
    // 游戏
    { prop: 'game_project', label: $t('text_game') },
    // 语言
    { prop: 'lang', label: $t('text_lang') },
    // 操作
    { prop: 'operator', label: $t('text_operator') },
    // 状态
    { prop: 'status', label: $t('text_result') },
  ];
});

const searchHandle = () => {
  currentPage.value = 1; // 重置为第一页
  _table.value.getData();
};

// 页码变更处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  _table.value?.getData();
};

// 获取状态标签类型-10为训练中，20训练失败，30训练成功
const getStatusTagType = (status: number) => {
  switch (status) {
    case 10:
      return 'warning';
    case 20:
      return 'danger';
    case 30:
      return 'success';
    default:
      return 'info';
  }
};

// 获取状态文本-10为训练中，20训练失败，30训练成功
const getStatusText = (status: number) => {
  switch (status) {
    case 10:
      return $t('text_training_in_progress');
    case 20:
      return $t('text_training_failed');
    case 30:
      return $t('text_training_success');

    default:
      return status;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  searchHandle();
});
</script>

<style scoped>
.page-view-wapper {
  padding: 20px;
}

/* 表格底部分页样式 */
.pagination-container {
  margin-top: 15px;
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.total-records {
  margin-right: 15px;
  color: #606266;
  font-size: 14px;
}

/* 自定义分页器样式 */
:deep(.el-pagination) {
  --el-pagination-button-color: #606266;
  --el-pagination-button-disabled-color: #c0c4cc;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background-color: #f4f4f5;
  color: #606266;
  border-radius: 2px;
}

:deep(.el-pagination .el-pager li) {
  background-color: #f4f4f5;
  color: #606266;
}

:deep(.el-pagination .el-pager li.is-active) {
  background-color: #1890ff;
  color: #ffffff;
}
</style>
