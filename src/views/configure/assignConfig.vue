<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="small">
        <el-form-item :label="`${$t('text_user_value')}：`">
          <el-input prefix-icon="UserFilled" v-model="searchForm.account" :placeholder="$t('know_m_rich_placeholder')"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Plus" plain @click="addConfigHanle">{{ $t('text_add_config') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="assignConfigList" :params="params">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
          <template #default="scope">
            <template v-if="item.prop === 'detail'">
              <span v-for="(v, index) in scope.row[item.prop]" :key="index">{{ v.game}}{{ index ===
                scope.row[item.prop].length - 1 ? '' : '、'}}</span>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('btn_op')" width="200" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="editHandle(scope.row)">{{ $t('btn_edit') }}</el-button>
            <el-popconfirm title="删除确认" @confirm="delHandle(scope.row)">
              <template #reference>
                <el-button link type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <editAssignConfig v-if="assignEditVisible" v-model:visible="assignEditVisible" :edit-data="assignConfigData"
      @success="searchHandle" />
  </div>
</template>

<script lang="ts">
import { assignConfigList, assignConfigDelete } from '@/api/assignConfig'
import { useI18n } from 'vue-i18n'
import editAssignConfig from './components/editAssignConfig.vue'
export default defineComponent({
  name: 'AssignConfig',
  components: {
    editAssignConfig
  }
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const _table = ref()
const searchForm = ref({
  group_desc: '',
  account: ''
})
const params = computed(() => {
  return {
    ...searchForm.value
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'account', label: $t('text_user_value') },
    { prop: 'detail', label: $t('text_game') },
    { prop: 'updated_at', label: $t('text_update_at') },
    { prop: 'operator', label: $t('text_update_by') }
  ]
})
const searchHandle  = () => {
  _table.value.getData()
}
const assignEditVisible = ref(false)
const assignConfigData = ref({})
const addConfigHanle = () => {
  assignConfigData.value = {}
  assignEditVisible.value = true
}
const editHandle = (row: Record<string, unknown>) => {
  assignConfigData.value = row
  assignEditVisible.value = true
}
const delHandle = async(row: Record<string, unknown>) => {
  try {
    await assignConfigDelete({ id: row.id, user: row.account})
    ElMessage.success($t('text_del_success'))
    searchHandle()
  } catch (error) {
    console.log(error)
  }
}
</script>