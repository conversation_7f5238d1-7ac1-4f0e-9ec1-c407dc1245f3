<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item :label="`${$t('text_game')}：`">
          <el-select v-model="searchForm.project" :placeholder="$t('place_select')" clearable filterable style="width: 180px">
            <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
              :value="v.game_project"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" icon="Plus" plain @click="batchAddHandle">{{ $t('text_batch_upload')
            }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="gameGoodsConfigList" :params="params">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
          <template #default="scope">
            {{ scope.row[item.prop] || '--' }}
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <batchAddGameGoods v-if="batchAddVisible" v-model:visible="batchAddVisible" @success="searchHandle" />
  </div>
</template>

<script lang="ts">
import { gameGoodsConfigList } from '@/api/gameGoodsConfig'
import { useI18n } from 'vue-i18n'
import batchAddGameGoods from './components/batchAddGameGoods.vue'
import { useEnumStore } from '@/stores'
export default defineComponent({
  name: 'GameGoodsConfig',
  components: { batchAddGameGoods }
})
</script>
<script setup lang="ts">
const gameList = computed(() => useEnumStore().gameList)
const { t: $t } = useI18n()
const _table = ref()
const searchForm = ref({
  project: ''
})
const params = computed(() => {
  return {
    ...searchForm.value
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'item_id', label: $t('text_goods_id') },
    { prop: 'item_name', label: $t('text_goods_name') },
    { prop: 'updated_at', label: $t('text_update_time') },
    { prop: 'operator', label: $t('text_update_by') },
  ]
})
const searchHandle = () => {
  _table.value.getData()
}

const batchAddVisible = ref(false)
const batchAddHandle = () => {
  batchAddVisible.value = true
}
</script>