<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item>
          <el-input prefix-icon="Memo" v-model="searchForm.module_name" :placeholder="$t('text_template_name')"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-input prefix-icon="Document" v-model="searchForm.content" :placeholder="$t('text_template_content')"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.game_project" :placeholder="$t('text_linked_game')" clearable filterable style="width: 180px" @change="handleGameChange">
            <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
              :value="v.game_project"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-cascader
            v-model="searchForm.cat_id"
            :options="templateTypes"
            :placeholder="$t('text_tpl_type')"
            collapse-tags
            :reserve-keyword="false"
            collapse-tags-tooltip
            :max-collapse-tags="1"
            :props="cascaderProps"
            clearable
            :disabled="!searchForm.game_project"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" icon="Plus" plain @click="addTempLibHandle">{{
            $t('text_new_add_template')
            }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" icon="Edit" @click="editTempType">{{
            $t('text_new_template_type')
            }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="tempLibList" :params="params" tooltip-effect="dark" class="custom-table">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'"
          :show-overflow-tooltip="item.showTooltip">
          <template #default="scope">
            <template v-if="item.prop === 'game_project'">
              <span>{{ gameList[scope.row.game_project]?.app_name || '--' }}</span>
            </template>
            <template v-else-if="item.prop === 'content'">
              <span @mouseover="overPreview(scope.row.id, scope.row.content)" @mouseout="outPreview">{{
                stripTags(scope.row.content) }}</span>
            </template>
            <template v-else-if="item.prop === 'enable'">
              <el-switch v-model="scope.row[item.prop]" :active-value="1" :inactive-value="2" active-color="#13ce66"
                inline-prompt inactive-color="#ff4949" :active-text="$t('text_enable')"
                :inactive-text="$t('text_disable')" @change="switchEnable($event, scope.row)">
              </el-switch>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('btn_op')" width="200" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="editHandle(scope.row)">{{ $t('btn_edit') }}</el-button>
            <el-popconfirm :title="$t('text_delete_sure')" @confirm="delHandle(scope.row)">
              <template #reference>
                <el-button link type="danger">{{ $t('text_delete')}}</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <!-- 表格图片预览 -->
    <div class="image_preview">
      <el-image-viewer hide-on-click-modal @close="()=>{showViewer = false}" v-if="showViewer"
        :url-list="previewList" />
    </div>
    <editTemplate v-if="tempEditVisible" v-model:visible="tempEditVisible" :edit-data="tempLibData" @success="searchHandle" />
    <editTemptype v-if="tempTypeEditVisible" v-model:visible="tempTypeEditVisible" @success="searchHandle"/>
    <el-dialog v-model="dialogVisible" width="600px" :title="$t('text_template_content')" :destroy-on-close="true" :close-on-click-modal="false">
      <div class="content-height" @click="getImg($event)">
        <div v-html="csContent"></div>
      </div>
      <template #footer>
        <el-button @click="() => {
          dialogVisible = false
          outPreview
        }">{{ $t('btn_cancel') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { tempLibList, editEnableTempLib, delTempLib, templateTypeList as fetchTemplateTypeList } from '@/api/templateLib'
import { useI18n } from 'vue-i18n'
import editTemplate from './components/editTemplate.vue'
import editTemptype from './components/editTempType.vue'
import { useEnumStore } from '@/stores'
export default defineComponent({
  name: 'TemplateConfig',
  components: {
    editTemplate,
    editTemptype,
    ElMessage
  }
})
interface Column {
  prop: string
  label: string
  width?: string
  showTooltip?: boolean
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const gameList = computed(() => useEnumStore().gameList)
const _table = ref()
const dialogVisible = ref(false)
const previewTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const csContent = ref('')
const searchForm = ref({
  module_name: '',
  content: '',
  game_project: '',
  cat_id: []
})
const params = computed(() => {
  return {
    ...searchForm.value
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'id', label: $t('text_template_id'), width: '120'},
    { prop: 'module_name', label: $t('text_template_name') },
    { prop: 'content', label: $t('text_template_content'), showTooltip: true },
    { prop: 'game_project', label: $t('text_linked_game') },
    { prop: 'category', label: $t('text_tpl_type') },
    { prop: 'operator', label: $t('text_operator')},
    { prop: 'update_time', label: $t('text_created_at') },
    { prop: 'enable', label: $t('text_status'), width: '100'}
  ]
})
const searchHandle = () => {
  _table.value.getData()
}

const tempEditVisible = ref(false)
const tempLibData = ref({})
const showViewer = ref<boolean>(false)
const previewList = ref<string[]>([])
const addTempLibHandle = () => {
  tempLibData.value = {}
  tempEditVisible.value = true
}

const switchEnable = (value: number, row: Record<string, unknown>) => {
  editEnableTempLib({
    id: row.id,
    enable: value
  })
  .then(() => {
    ElMessage.success($t('text_status_success'))
  }).catch((error: unknown) => {
    console.log(error)
  }).finally(() => {
    searchHandle()
  })
}
const editHandle = (row: Record<string, unknown>) => {
  tempLibData.value = row
  tempEditVisible.value = true
}
const delHandle = async(row: Record<string, unknown>) => {
  await delTempLib({ id: row.id})
  ElMessage.success($t('text_del_success'))
  searchHandle()
}
const getImg = ($event: MouseEvent) => {
  let target = $event.target as HTMLImageElement | null
  // 确保事件目标本身是图片
  if (target && target instanceof HTMLImageElement && target.tagName === 'IMG' && target.src) {
    previewList.value = [target.src]
    showViewer.value = true
  }
}
const overPreview = (id: number, content: string) => {
  // 清除之前的定时器（如果存在）  
  if (previewTimer.value !== null) {  
    clearTimeout(previewTimer.value)
  }
  // 设置新的定时器
  previewTimer.value = setTimeout(() => {
    dialogVisible.value = true
    csContent.value = content
  }, 1000)
}
const outPreview = () => {
  if (previewTimer.value) {
    clearTimeout(previewTimer.value)
    previewTimer.value = null
  }
}
// 清洗html标签
const stripTags = (htmlContent: any) => {
  const doc = new DOMParser().parseFromString(htmlContent, 'text/html')
  return doc.body.textContent || ""
}
// 编辑模板类型
const tempTypeEditVisible = ref(false)
const editTempType = () => {
  console.log('编辑模板类型')
  tempTypeEditVisible.value = true
}

// 配置cascaderProps
const cascaderProps = {
  multiple: true,
  emitPath: false,
  value: 'cat_id',
  label: 'category'
}

// 获取模板类型数据
const templateTypes = ref([])
const fetchTemplateTypes = async () => {
  const res = await fetchTemplateTypeList({ project: searchForm.value.game_project })
  templateTypes.value = res.data
}

// 处理游戏改变
const handleGameChange = (value: string) => {
  searchForm.value.cat_id = []
  templateTypes.value = []
  if (value) {
    fetchTemplateTypes()
  }
}
</script>
 
<style lang="scss">
.custom-table {
  .el-popper{font-size: 14px; max-width:20% }
}
.el-dialog__body {
  .content-height {
    max-height: 450px;
    overflow-y: auto;
    .ql-align-right {
      img {
        width: 200px;
        height: 200px;
      }
    }
  }
}
</style>