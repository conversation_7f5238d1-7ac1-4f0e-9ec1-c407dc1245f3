<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="small">
        <el-form-item :label="`${$t('text_team')}：`">
          <el-input prefix-icon="Management" v-model="searchForm.team_name"
            :placeholder="$t('know_m_rich_placeholder')" clearable />
        </el-form-item>
        <el-form-item :label="`${$t('text_user_value')}：`">
          <el-input prefix-icon="UserFilled" v-model="searchForm.team_member" :placeholder="$t('know_m_rich_placeholder')"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Plus" plain @click="addConfigHanle">{{ $t('text_add_config') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="getTeamList" :params="params">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
          <template #default="scope">
            <template v-if="item.prop === 'game'">
              <span v-for="(v, index) in scope.row[item.prop]" :key="index">{{ v }}{{ index ===
                scope.row[item.prop].length - 1 ? '' : '、' }}</span>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('btn_op')" width="200" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="editHandle(scope.row)">{{ $t('btn_edit') }}</el-button>
            <el-popconfirm :title="$t('text_delete_sure')" @confirm="delHandle(scope.row)">
              <template #reference>
                <el-button link type="danger">{{ $t('text_delete') }}</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <editTeamConfig v-if="teamEditVisible" v-model:visible="teamEditVisible" :edit-data="teamConfigData"
      @success="searchHandle" />
  </div>
</template>

<script lang="ts">
import { getTeamList, teamConfigDel } from '@/api/assignConfig'
import { useI18n } from 'vue-i18n'
import editTeamConfig from './components/editTeamConfig.vue'
export default defineComponent({
  name: 'TeamConfig',
  components: {
    editTeamConfig
  }
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const _table = ref()
const searchForm = ref({
  team_name: '',
  team_member: ''
})
const params = computed(() => {
  return {
    ...searchForm.value
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'team_id', label: $t('text_group_id') },
    { prop: 'team_name', label: $t('text_team') },
    { prop: 'team_member', label: $t('text_team_user') },
    { prop: 'update_time', label: $t('text_update_at') },
    { prop: 'updater', label: $t('text_update_by') }
  ]
})
const searchHandle  = () => {
  _table.value.getData()
}
const teamEditVisible = ref(false)
const teamConfigData = ref({})
const addConfigHanle = () => {
  teamConfigData.value = {}
  teamEditVisible.value = true
}
const editHandle = (row: Record<string, unknown>) => {
  teamConfigData.value = row
  teamEditVisible.value = true
}
const delHandle = async(row: Record<string, unknown>) => {
  try {
    await teamConfigDel({ team_id: row.team_id})
    ElMessage.success($t('text_del_success'))
    searchHandle()
  } catch (error) {
    console.log(error)
  }
}
</script>
