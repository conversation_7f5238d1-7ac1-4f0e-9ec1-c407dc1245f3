<template>
  <div class="page-view-wapper">
    <el-tabs v-model="activeName" class="tabs-box">
      <el-tab-pane :label="$t('text_ticket')" name="ticket">
        <ticketTagPage />
      </el-tab-pane>
      <el-tab-pane label="DC" name="dc">
        <dcTagPage />
      </el-tab-pane>
      <el-tab-pane label="LINE" name="line">
        <lineTagPage />
      </el-tab-pane>
      <el-tab-pane label="MAIL" name="mail">
        <mailTagPage />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import ticketTagPage from './components/ticketTagPage.vue';
import dcTagPage from './components/dcTagPage.vue';
import lineTagPage from './components/lineTagPage.vue';
import mailTagPage from './components/mailTagPage.vue';
export default defineComponent({
  name: 'TagLibraryConfig',
  components: {
    ticketTagPage,
    dcTagPage,
    lineTagPage,
    ElMessage,
  },
});
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const activeName = ref('ticket');
</script>
<style lang="scss" scoped>
.page-view-wapper {
  &:deep(.el-tabs) {
    height: 100%;
    padding: 0 20px;
  }
  &:deep(.el-tabs__content) {
    height: calc(100% - 70px) !important;
    overflow: auto;
  }
  &:deep(.el-scrollbar__view) {
    height: calc(100vh - 270px) !important;
  }
}
</style>
