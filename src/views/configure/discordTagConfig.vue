<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item :label="`${$t('text_tag_name')}：`">
          <el-input prefix-icon="PriceTag" v-model="searchForm.tag_name" :placeholder="$t('know_m_rich_placeholder')"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" icon="Plus" plain @click="addTagLibHandle">{{ $t('btn_add_tag')
            }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="discordTagList" :params="params">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
          <template #default="scope">
            <template v-if="item.prop === 'status'">
              <el-switch
                v-model="scope.row[item.prop]"
                :active-value="1"
                :inactive-value="2"
                active-color="#13ce66"
                inline-prompt
                inactive-color="#ff4949"
                :active-text="$t('text_enable')"
                :inactive-text="$t('text_disable')"
                @change="switchEnable($event as boolean, scope.row)"
              >
              </el-switch>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('btn_op')"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="editHandle(scope.row)"
            >{{ $t('btn_edit') }}</el-button>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <editDiscordTag v-if="tagEditVisible" v-model:visible="tagEditVisible" :edit-data="discordTagData" @success="searchHandle" />
  </div>
</template>

<script lang="ts">
import { discordTagList, editDiscord } from '@/api/tagLib'
import { useI18n } from 'vue-i18n'
import editDiscordTag from './components/editDiscordTag.vue'
export default defineComponent({
  name: 'DiscordTagConfig',
  components: {
    editDiscordTag,
    ElMessage
  }
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const _table = ref()
const searchForm = ref({
  tag_name: ''
})
const params = computed(() => {
  return {
    ...searchForm.value
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'id', label: 'ID', width: '120'},
    { prop: 'tag_name', label: $t('text_tag_name') },
    { prop: 'tag_desc', label: $t('text_tag_desc') },
    { prop: 'update_time', label: $t('text_update_at') },
    { prop: 'operator', label: $t('text_update_by')},
    { prop: 'status', label: $t('text_status'), width: '100'}
  ]
})
const searchHandle = () => {
  _table.value.getData()
}

const tagEditVisible = ref(false)
const discordTagData = ref({})
const addTagLibHandle = () => {
  discordTagData.value = {}
  tagEditVisible.value = true
}

const switchEnable = async (value: boolean, row: Record<string, unknown>) => {
  try {
    await editDiscord({
      id: row.id,
      status: value
    })
    ElMessage.success($t('text_status_success'))
    searchHandle()
  } catch (error) {
    console.log(error)
    row.status = !value
  }
}
const editHandle = (row: Record<string, unknown>) => {
  discordTagData.value = row
  tagEditVisible.value = true
}
</script>