<template>
  <div class="page-view-wapper">
    <el-tabs v-model="activeName" class="tabs-box">
      <el-tab-pane label="DC" name="dc">
        <div class="search-box">
          <el-form :inline="true" :model="searchForm" size="small">
            <!-- 游戏 -->
            <el-form-item :label="`${$t('text_game')}：`">
              <el-select v-model="searchForm.game_project" :placeholder="$t('place_select')" multiple collapse-tags
                collapse-tags-tooltip clearable filterable :reserve-keyword="false" style="width: 180px">
                <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
                  :value="v.game_project"></el-option>
              </el-select>
            </el-form-item>
            <!-- VIP专员 -->
            <el-form-item :label="`${$t('text_vip_commissioner')}：`">
              <el-select v-model="searchForm.maintainer" :placeholder="$t('place_select')" multiple collapse-tags
                collapse-tags-tooltip clearable filterable :reserve-keyword="false" style="width: 180px">
                <el-option v-for="(v, index) in maintainerList" :key="index" :label="v.account"
                  :value="v.account"></el-option>
              </el-select>
            </el-form-item>
            <!-- DC账号 -->
            <el-form-item :label="`${$t('text_player_dc')}：`">
              <el-input v-model="searchForm.nick_name" :placeholder="$t('place_input') " clearable />
            </el-form-item>
            <!-- 玩家DC ID -->
            <el-form-item :label="`${$t('text_player_dcid')}：`">
              <el-input v-model="searchForm.dsc_user_id" :placeholder="$t('place_input') " clearable />
            </el-form-item>
            <!-- VIP状态 -->
            <el-form-item :label="`${$t('text_vip_state')}：`">
              <el-select v-model="searchForm.vip_state" :placeholder="$t('place_select')" clearable style="width: 120px">
                <el-option v-for="(v, index) in enumList.PlayerVipState" :key="index" :label="v.name"
                  :value="v.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button size="small" type="primary" icon="Plus" plain @click="addRelationHandle">{{ $t('text_new_add')
                }}</el-button>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Download" @click="donwLoad" v-has="'relationship:dc:download'" :loading="progState"
                :disabled="progState">{{ $t('text_data_export') }}
                <span v-if="progState">{{ progressNum + '%' }}</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="page-content-box">
          <ops-table ref="_table" :data-api="relationList" :params="params" tooltip-effect="dark" class="custom-table">
            <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
              :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'"
              :show-overflow-tooltip="item.showTooltip">
              <template #default="scope">
                <template v-if="item.prop === 'vip_state'">
                  <el-switch v-model="scope.row[item.prop]" :active-value="2" :inactive-value="1" active-color="#13ce66"
                    inline-prompt inactive-color="#ff4949" :active-text="'VIP'" :inactive-text="$t('text_no_vip')"
                    @change="switchEnable($event as number, scope.row)">
                  </el-switch>
                </template>
                <template v-else>
                  {{ scope.row[item.prop] || '--' }}
                </template>
              </template>
            </el-table-column>
            <el-table-column fixed="right" :label="$t('btn_op')" width="200" align="center">
              <template #default="scope">
                <el-button link type="primary" @click="editHandle(scope.row)">{{ $t('btn_edit') }}</el-button>
                <el-popconfirm :title="$t('text_delete_sure')" @confirm="delHandle(scope.row)">
                  <template #reference>
                    <el-button link type="danger">{{ $t('text_delete')}}</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </ops-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="LINE" name="line">
        <div class="search-box">
          <el-form :inline="true" :model="searchForm" size="small">
            <!-- 游戏 -->
            <el-form-item :label="`${$t('text_game')}：`">
              <el-select v-model="searchForm.project" :placeholder="$t('place_select')" multiple collapse-tags
                collapse-tags-tooltip clearable filterable :reserve-keyword="false" style="width: 180px">
                <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
                  :value="v.game_project"></el-option>
              </el-select>
            </el-form-item>
            <!-- VIP专员 -->
            <el-form-item :label="`${$t('text_vip_commissioner')}：`">
              <el-select v-model="searchForm.maintainer" :placeholder="$t('place_select')" multiple collapse-tags
                collapse-tags-tooltip clearable filterable :reserve-keyword="false" style="width: 180px">
                <el-option v-for="(v, index) in maintainerList" :key="index" :label="v.account"
                  :value="v.account"></el-option>
              </el-select>
            </el-form-item>
            <!-- 玩家line昵称 -->
            <el-form-item :label="`${$t('text_player_line_name')}：`">
              <el-input v-model="searchForm.nick_name" :placeholder="$t('place_input') " clearable />
            </el-form-item>
            <!-- 玩家line ID -->
            <el-form-item :label="`${$t('text_player_line_id')}：`">
              <el-input v-model="searchForm.line_user_id" :placeholder="$t('place_input') " clearable />
            </el-form-item>
            <!-- VIP状态 -->
            <el-form-item :label="`${$t('text_vip_state')}：`">
              <el-select v-model="searchForm.vip_state" :placeholder="$t('place_select')" clearable style="width: 120px">
                <el-option v-for="(v, index) in enumList.PlayerVipState" :key="index" :label="v.name"
                  :value="v.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Download" @click="donwLoad" v-has="'relationship:line:download'" :loading="progState"
                :disabled="progState">{{ $t('text_data_export') }}
                <span v-if="progState">{{ progressNum + '%' }}</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="page-content-box">
          <ops-table ref="line_table" :data-api="lineRelationList" :params="params" tooltip-effect="dark" class="custom-table">
            <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
              :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'"
              :show-overflow-tooltip="item.showTooltip">
              <template #default="scope">
                <template v-if="item.prop === 'vip_state'">
                  <el-switch v-model="scope.row[item.prop]" :active-value="2" :inactive-value="1" active-color="#13ce66"
                    inline-prompt inactive-color="#ff4949" :active-text="'VIP'" :inactive-text="$t('text_no_vip')"
                    @change="switchEnable($event as number, scope.row)">
                  </el-switch>
                </template>
                <template v-else>
                  {{ scope.row[item.prop] || '--' }}
                </template>
              </template>
            </el-table-column>
            <el-table-column fixed="right" :label="$t('btn_op')" width="200" align="center">
              <template #default="scope">
                <el-button link type="primary" @click="editHandle(scope.row)">{{ $t('btn_edit') }}</el-button>
                <el-popconfirm :title="$t('text_delete_sure')" @confirm="delHandle(scope.row)">
                  <template #reference>
                    <!-- <el-button link type="danger">{{ $t('text_delete')}}</el-button> -->
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </ops-table>
        </div>
      </el-tab-pane>
    </el-tabs>
    <editRelationShip v-if="relationEditVisible" v-model:visible="relationEditVisible" :get-game="game"
      :edit-data="relationData" @success="searchHandle" />
  </div>
</template>

<script lang="ts">
import { relationList, delRelationLib, editRelationLib, getUserLists, exportLib, lineRelationList, lineExportLib, lineEditRelationLib, lineDelRelationLib } from '@/api/relationship'
import { useI18n } from 'vue-i18n'
import editRelationShip from './components/editRelationShip.vue'
import { useEnumStore, useAppStore } from '@/stores'
export default defineComponent({
  name: 'TagLibraryConfig',
  components: {
    editRelationShip,
    ElMessage
  }
})
interface SearchForm {
  game_project: string[]
  nick_name: string
  maintainer: string[]
  dsc_user_id: string
  vip_state: number | null
  line_user_id: string
  project: string[]
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const gameList = computed(() => useEnumStore().gameList)
const enumList = computed(() => useEnumStore().enumList)
const game = ref('')
const _table = ref()
const line_table = ref()
const maintainerList= ref([])
const activeName = ref('dc')
const searchForm = ref<SearchForm>({
  dsc_user_id: '',
  maintainer: [],
  nick_name: '',
  game_project: [],
  vip_state: null,
  line_user_id: '',
  project: []
})
const params = computed(() => {
  return {
    ...searchForm.value,
    nick_name: [searchForm.value.nick_name].filter(Boolean)
  }
})
const columns = computed((): Column[] => {
  return [
    ...(activeName.value === 'dc' ? [{ prop: 'dsc_user_id', label: $t('text_player_dc_id') }] :
      [{ prop: 'line_user_id', label: $t('text_player_line_id') }]),
    ...(activeName.value === 'dc' ? [{ prop: 'nick_name', label: $t('text_player_dc_name') }] :
      [{ prop: 'nick_name', label: $t('text_player_line_name') }]),
    { prop: 'fpid', label: $t('text_correlation_player_fpid') },
    { prop: 'sid', label: $t('text_server') },
    { prop: 'lang', label: $t('text_lang') },
    { prop: 'birthday', label: $t('text_birthday') },
    { prop: 'vip_state', label: $t('text_vip_state') },
    { prop: 'maintainer', label: $t('text_correlation_commissioner') },
    { prop: 'operator', label: $t('text_operator')},
    { prop: 'update_time', label: $t('text_created_at') },
  ]
})
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum)
const progState = ref<boolean>(false)
watch(progressNum, (n) => {
  progState.value = (n < 100 && n > -1) ? true : false
})

const searchHandle = () => {
  (activeName.value === 'dc' ? _table : line_table).value.getData()
}

const relationEditVisible = ref(false)
const relationData = ref({})
// const addRelationHandle = () => {
//   relationData.value = {}
//   relationEditVisible.value = true
// }

const editHandle = (row: Record<string, unknown>) => {
  relationData.value = row
  relationEditVisible.value = true
}
const switchEnable = async (value: number, row: Record<string, unknown>) => {
  try {
    // const { id, vip_state } = row
    await (activeName.value === 'dc' ? editRelationLib(row) : lineEditRelationLib(row))
    ElMessage.success($t('text_status_success'))
    searchHandle()
  } catch (error) {
    console.log(error)
  }
}
// 删除
const delHandle = async(row: Record<string, unknown>) => {
  try {
    const { id, dsc_user_id, game_project, line_user_id, project } = row
    await (activeName.value === 'dc' ? delRelationLib({ id, dsc_user_id, game_project }) :
      lineDelRelationLib({ id, line_user_id, project }))
    ElMessage.success($t('text_del_success'))
    searchHandle()
  } catch (error) {
    console.log(error)
  }
}
// 导出
const donwLoad = () => {
  if (activeName.value === 'dc') {
    exportLib({...searchForm.value, nick_name: [searchForm.value.nick_name].filter(Boolean)})
  } else if (activeName.value === 'line') {
    lineExportLib({...searchForm.value, nick_name: [searchForm.value.nick_name].filter(Boolean)})
  }
}
// 获取专员
const getMaintainer =() => {
  getUserLists({}).then((res: any) => {
    maintainerList.value = res
  })
}
getMaintainer()
</script>
<style lang="scss" scoped>
.page-view-wapper {
  &:deep(.el-tabs) {
    height: 100%;
    padding: 0 20px;
  }
  &:deep(.el-tabs__content) {
    height: calc(100% - 65px)!important;
    overflow: auto;
  }
  &:deep(.el-scrollbar__view) {
    height: calc(100vh - 320px)!important;
  }
}
</style>