<template>
  <el-dialog
    v-model="value"
    width="600px"
    class="category-dialog"
    :title="$t('text_new_template_type')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      size="small"
      :inline="true"
      label-position="right"
      :model="form"
      :rules="rules"
      ref="formRef"
    >
      <el-form-item :label="$t('text_game')" prop="game_project">
        <el-select
          v-model="form.game_project"
          :placeholder="$t('place_select')"
          clearable filterable
          @change="handleGameChange"
          style="width: 300px;"
        >
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          size="small"
          type="primary"
          icon="Plus"
          plain
          :disabled="!form.game_project"
          @click="addFirstLevelTemplateType"
        >{{ $t('text_one_type') }}</el-button>
      </el-form-item>
    </el-form>
    <div class="page-tag-content">
      <div class="menu-wrap" v-loading="loadingTree">
        <el-tree
          class="category-tree"
          :data="treeData"
          :default-expanded-keys="[0]"
          node-key="cat_id"
          ref="tree"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div>{{ data.category }}</div>
              <div @click.stop>
                <el-button
                  v-if="data.level !== 0"
                  type="primary"
                  text
                  size="small"
                  icon="EditPen"
                  @click="() => editTemplateTypeNode(node, data)"
                >
                </el-button>
                <el-popconfirm
                  v-if="data.level !== 0"
                  class="ml-10"
                  :title="$t('text_delete_sure')"
                  @confirm="deleteTemplateType(node, data)"
                >
                  <template #reference>
                    <el-button
                      class="delete-btn"
                      type="danger"
                      text
                      size="small"
                      icon="Delete"
                    >
                    </el-button>
                  </template>
                </el-popconfirm>
                <el-button
                  :disabled="data.level === 3"
                  type="primary"
                  text
                  class="ml-10"
                  size="small"
                  icon="Plus"
                  @click="() => addSubTemplateType(node, data)"
                >{{ $t('btn_next_level') }}
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <el-dialog
        v-model="titleDialogVisible"
        :title="editType === 1 ? $t('text_add_template_type') : $t('text_edit_template_type')"
        width="400px"
        :destroy-on-close="true"
        :before-close="closeTitleDialog"
        :close-on-click-modal="false"
        align-center
      >
        <el-form :model="tagForm" :rules="rulesQues" label-position="top" ref="formRef" v-loading="loadingDialog">
          <el-form-item :label="$t('text_template_type_name')" prop="category">
            <el-input v-model="tagForm.category" clearable :placeholder="$t('place_input')" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="closeTitleDialog">{{ $t('btn_cancel') }}</el-button>
          <el-button type="primary" :disabled="!tagForm.category" @click="submitTemplateType">{{ $t('btn_ok') }}</el-button>
        </template>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { useEnumStore } from '@/stores'
import { templateTypeList, addTemplateType, editTemplateType, delTemplateType } from '@/api/templateLib'
export default defineComponent({
  name: 'EditTempType',
  components: {
    ElMessage
  }
})
interface EditTempLibProps {
  visible: boolean
}
interface Form {
  game_project: string
}
interface TreeNode {
  cat_id: number
  category: string
  level?: number
  children?: TreeNode[]
}
interface Params {
  cat_level?: number
  parent_id?: number
  cat_id?: number
  category?: string
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const gameList = computed(() => useEnumStore().gameList)
const loadingTree = ref(false)
const loadingDialog = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  game_project: ''
})
const rules = reactive<FormRules>({
  module_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  content: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  game_project: [{ required: true, message: $t('place_select'), trigger: 'change' }]
})
const treeData = ref<TreeNode[]>([])
const titleDialogVisible = ref(false)
const editType = ref(1) // 1-新增 2-编辑
const tagForm = reactive({
  category: ''
})
const newParams = ref<Params>({})
const fetchTemplateTypeList = () => {
  if(form.game_project) {
    loadingTree.value = true
    treeData.value = []
    templateTypeList({
      project: form.game_project,
    }).then((res: any) => {
      treeData.value = res.data
    }).finally(()=> {
      loadingTree.value = false
    })
  }
}
const handleGameChange = (val: string) => {
  form.game_project = val
  fetchTemplateTypeList()
}
const addFirstLevelTemplateType = () => {
  titleDialogVisible.value = true
  editType.value = 1
  newParams.value = {
    cat_level: 1,
    parent_id: 0,
  }
}
const addSubTemplateType = (node: any, data: object) => {
  titleDialogVisible.value = true
  editType.value = 1
  newParams.value = {
    cat_level: node.data.level + 1,
    parent_id: node.data.cat_id,
  }
}
const editTemplateTypeNode = (node: any, data: object) => {
  titleDialogVisible.value = true
  editType.value = 2
  newParams.value = {
    cat_id: node.data.cat_id,
    cat_level: node.data.level,
  }
  tagForm.category = node.data.category
}
const deleteTemplateType = (node: any, data: object) => {
  if (loadingTree.value) return
  loadingTree.value = true
  delTemplateType({ cat_id: node.data.cat_id })
    .then(() => {
      fetchTemplateTypeList()
      ElMessage.success($t('text_del_success'))
    }).catch((error: string) => {
      console.log(error)
      loadingTree.value = false
    })
}
const submitTemplateType = () => {
  formRef.value!.validate((valid) => {
    if (!valid || loadingDialog.value) return
    loadingDialog.value = true
    const request = editType.value === 1
      ? addTemplateType({
          ...tagForm,
          ...newParams.value,
          project: form.game_project,
        })
      : editTemplateType({
          category: tagForm.category,
          ...newParams.value
        })
    request.then(() => {
      ElMessage.success($t('text_add_success'))
      closeTitleDialog()
      fetchTemplateTypeList()
    }).catch((error: string) => {
      console.log(error)
    }).finally(() => {
      loadingDialog.value = false
    })
  })
}
const closeTitleDialog = () => {
  tagForm.category = ''
  titleDialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.ml-10 {
  margin-left: 10px;
}
.category-dialog {
  &:deep(.el-dialog__body) {
    padding: 20px;
    min-height: 300px;
    max-height: 600px;
    overflow: auto;
  }
  .menu-wrap {
    margin-top: 10px;
  }
  &:deep(.el-tree-node__content) {
    height: 30px;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    &:deep(.el-button--small) {
      padding: 0;
    }
  }
  .delete-btn {
    &.el-button--text {
      color: #f56c6c;
      &.is-active,
      &:active {
        color: #dd6161;
      }
      &:focus,
      &:hover {
        color: #f78989;
      }
    }
  }
}
</style>