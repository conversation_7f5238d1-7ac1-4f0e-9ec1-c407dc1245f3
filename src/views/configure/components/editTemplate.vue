<template>
  <el-dialog v-model="value" width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_template') : $t('text_add_template')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" size="small" :model="form" :rules="rules" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_template_name')" prop="module_name">
        <el-input v-model="form.module_name" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <el-form-item :label="$t('text_linked_game')" prop="game_project">
        <el-select v-model="form.game_project" :placeholder="$t('place_select')" clearable filterable @change="handleGameProjectChange">
          <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
            :value="v.game_project"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('text_template_category')" prop="cat_id">
        <el-cascader
          style="width: 100%"
          v-model="form.cat_id"
          :options="templateTypeOptions"
          :props="{ multiple: false, value: 'cat_id', label: 'category', emitPath: false }"
          :placeholder="$t('place_select')"
          :disabled="!form.game_project || cascaderLoading"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('text_template_content')" prop="content">
        <opsEditer v-model="form.content" v-model:uploading="isUploading"></opsEditer>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { saveTempLib, editEnableTempLib, templateTypeList } from '@/api/templateLib'
import { useEnumStore } from '@/stores'
export default defineComponent({
  name: 'EditTemplate',
  components: {
    ElMessage
  }
})
interface EditTempLibProps {
  visible: boolean
  editData?: Record<string, unknown>
}
interface Form {
  module_name: string
  content: string
  game_project: string
  cat_id: ''|number
  lib_id?: number
  id?: number
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const gameList = computed(() => useEnumStore().gameList)
const loading = ref(false)
const cascaderLoading = ref(false) // 新建标识变量
const formRef = ref<FormInstance>()
const isUploading = ref(false)
const form = reactive<Form>({
  module_name: '',
  content: '',
  game_project: '',
  cat_id: ''
})
const rules = reactive<FormRules>({
  module_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  content: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  game_project: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  cat_id: [{ required: true, message: $t('place_select'), trigger: 'change' }]
})
const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      if (Object.keys(props.editData).length > 0) {
        await editEnableTempLib(form)
      } else {
        await saveTempLib(form)
      }
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
// 模板分类选项
const templateTypeOptions = ref<any[]>([])
// 获取模板分类
const getTemplateTypes = async (): Promise<void> => {
  loading.value = true
  templateTypeList({ project: form.game_project })
    .then((res: { data: any[] }) => {
      templateTypeOptions.value = res.data
    })
    .catch((error: unknown) => {
      console.error(error)
    }).finally(() => {
      loading.value = false
    })
}

// 处理游戏项目变化
const handleGameProjectChange = async () => {
  form.cat_id = '' // 修改为单选
  if (form.game_project) {
    cascaderLoading.value = true
    await getTemplateTypes()
    cascaderLoading.value = false
  }
}

onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.id = props.editData.id as number
    form.module_name = props.editData.module_name as string
    form.game_project = props.editData.game_project as string
    handleGameProjectChange()
    form.cat_id = Number(props.editData.cat_id)
    form.content = props.editData.content as string
  }
})
</script>

<style scoped>
.upload_box {
  width: 100%;
}
</style>
