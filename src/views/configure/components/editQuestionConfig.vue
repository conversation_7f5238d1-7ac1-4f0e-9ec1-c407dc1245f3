<template>
  <el-dialog v-model="value" width="600px"
    :title="$t('编辑调查问卷')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" size="small" :model="form" :rules="rules" ref="formRef" v-loading="loading">
      <!-- 推送周期 -->
      <el-form-item :label="$t('推送周期')" prop="push_cycle">
        <el-radio-group v-model="form.push_cycle">
          <el-radio v-for="(v,k) in enumList.SurveyPushCycle" :key="k" :value="v.value">{{ v.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 推送时间 -->
      <el-form-item :label="$t('推送时间(UTC)')" prop="combined">
        <el-col :span="11">
          <ops-select v-model="form.push_week" :placeholder="$t('place_select')">
            <el-option label="周一" :value="1" />
            <el-option label="周二" :value="2" />
            <el-option label="周三" :value="3" />
            <el-option label="周四" :value="4" />
            <el-option label="周五" :value="5" />
            <el-option label="周六" :value="6" />
            <el-option label="周日" :value="7" />
          </ops-select>
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-time-picker
            v-model="form.push_time_web"
            type="date"
            value-format="HH:mm:ss"
            format="HH:mm:ss"
            :placeholder="$t('place_select')"
          />
        </el-col>
      </el-form-item>
      <!-- 配置生效日期 -->
      <el-form-item :label="$t('配置生效日期(UTC)')" prop="effective_time">
        <el-date-picker
          v-model="form.effective_time"
          type="date"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
          :placeholder="$t('place_select')"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <!-- 问卷有效期 -->
      <el-form-item :label="$t('问卷有效期')" prop="expire_time">
        <el-select v-model="form.expire_time" :placeholder="$t('place_select')" style="width: 200px;">
          <el-option v-for="(v,k) in enumList.SurveyEffective" :label="v.name" :value="v.value" :key="k" />
        </el-select>
      </el-form-item>
      <!-- 问卷标题 -->
      <el-form-item :label="$t('问卷标题')" required >
        <el-radio-group v-model="form.title_lang">
          <el-radio
            v-for="(v, index) in langList"
            :key="v.code + index"
            :label="v.name"
            :value="v.code"
          >{{v.name}}</el-radio>
        </el-radio-group>
        <el-input v-model="form.survey_titles[form.title_lang]" clearable :placeholder="$t('请输入内容')" class="mt-5" />
      </el-form-item>
      <!-- 推送文案 -->
      <el-form-item :label="$t('推送文案')">
        <el-radio-group v-model="form.contents_lang">
          <el-radio
            v-for="(v, index) in langList"
            :key="v.code + index"
            :label="v.name"
            :value="v.code"
          >{{v.name}}</el-radio>
        </el-radio-group>
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4}"
          placeholder="请输入内容"
          v-model="form.push_contents[form.contents_lang]"
          class="mt-5" />
      </el-form-item>
      <!-- 评星产品题 -->
      <el-form-item :label="$t('评星 -- 产品题')" required>
        <el-radio-group v-model="form.product_questions_lang">
          <el-radio
            v-for="(v, index) in langList"
            :key="v.code + index"
            :label="v.name"
            :value="v.code"
          >{{v.name}}</el-radio>
        </el-radio-group>
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4}"
          placeholder="请输入内容"
          v-model="form.product_questions[form.product_questions_lang]"
          class="mt-5" />
      </el-form-item>
      <!-- 评星服务题 -->
      <el-form-item :label="$t('评星 -- 服务题')" required>
        <el-radio-group v-model="form.service_questions_lang">
          <el-radio
            v-for="(v, index) in langList"
            :key="v.code + index"
            :label="v.name"
            :value="v.code"
          >{{v.name}}</el-radio>
        </el-radio-group>
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4}"
          placeholder="请输入内容"
          v-model="form.service_questions[form.service_questions_lang]"
          class="mt-5" />
      </el-form-item>
      <!-- 评1~3星填写理由题目 -->
      <el-form-item :label="$t('评1~3星填写理由题目')" required>
        <el-radio-group v-model="form.reasons_lang">
          <el-radio
            v-for="(v, index) in langList"
            :key="v.code + index"
            :label="v.name"
            :value="v.code"
          >{{v.name}}</el-radio>
        </el-radio-group>
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4}"
          placeholder="请输入内容"
          v-model="form.reasons[form.reasons_lang]"
          class="mt-5" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'

import type { FormInstance, FormRules } from 'element-plus'
import { questionnaireConfigSave, questionnaireConfigDetail } from '@/api/questionnaireConfig'
import { useEnumStore } from '@/stores'
export default defineComponent({
  name: 'EditQuestionConfig',
  components: {
    ElMessage
  }
})
interface EditTagLibProps {
  visible: boolean
  editData?: Record<string, unknown>
}

interface Form {
  id?: number
  project: string
  push_cycle: number
  push_week: number
  push_time_web: string
  effective_time: string
  expire_time: string
  title_lang: string
  survey_titles: Record<string, string>
  contents_lang: string
  push_contents: Record<string, string>
  product_questions_lang: string
  product_questions: Record<string, string>
  service_questions_lang: string
  service_questions: Record<string, string>
  reasons_lang: string
  reasons: Record<string, string>
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTagLibProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const langList = computed(() => useEnumStore().LangsList)
const enumList = computed(() => useEnumStore().enumList)
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = ref<Form>({
  project: '',
  push_cycle: 1,
  push_week: 0,
  push_time_web: '',
  effective_time: '',
  expire_time: '',
  title_lang: 'en',
  survey_titles: {en: ''},
  contents_lang: 'en',
  push_contents: {en: ''},
  product_questions_lang: 'en',
  product_questions: {en: ''},
  service_questions_lang: 'en',
  service_questions: {en: ''},
  reasons_lang: 'en',
  reasons: {en: ''},
})
const validateBothFields = (rule: any, value: any, callback: any) => {
  if (!form.value.push_week || !form.value.push_time_web) {
    callback(new Error('请选择推送时间'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules>({
  push_cycle: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  combined: [{ required: true, validator: validateBothFields, trigger: 'blur' }],
  effective_time: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  expire_time: [{ required: true, message: $t('place_select'), trigger: 'change' }]
})

const disabledDate = (time: any) => {
  return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
}
const submit = () => {
  console.log(form)
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      await questionnaireConfigSave(form.value)
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}
// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.value.id = props.editData.id as number
    form.value.project = props.editData.project as string
    loading.value = true
    questionnaireConfigDetail({id: form.value.id}).then((res: any) => {
      console.log(res)
      form.value = { ...form.value, ...res.data }
    }).finally(() => {
      loading.value = false
    })
  }
})
</script>

<style lang="scss" scoped>
.text-center {
  text-align: center;
}
.pm-question {
  padding: 10px 15px;
}
.service-question {
  padding: 0 15px;
}
.mt-5 {
  margin-top: 5px;
}
.el-radio {
  margin-right: 10px;
}
.upload_box {
  width: 100%;
}
</style>