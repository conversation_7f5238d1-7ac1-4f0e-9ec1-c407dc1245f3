<template>
  <div class="search-box">
    <el-form :inline="true" :model="searchTicketForm" size="small">
      <el-form-item :label="`${$t('text_tag_lib_name')}：`">
        <el-input prefix-icon="PriceTag" v-model="searchTicketForm.lib_name" :placeholder="$t('know_m_rich_placeholder')"
          clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="Plus" plain @click="addTagLibHandle">{{ $t('btn_add_tag_base')
          }}</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="page-content-box">
    <ops-table ref="_table" :data-api="tagLibList" :params="paramsTicket">
      <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
        :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
        <template #default="scope">
          <template v-if="item.prop === 'projects'">
            <span v-for="(v, index) in scope.row[item.prop]" :key="index">{{ v }}{{ index === scope.row.projects.length - 1 ? '' : '、' }}</span>
          </template>
          <template v-else-if="item.prop === 'enable'">
            <el-switch
              v-model="scope.row[item.prop]"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              inline-prompt
              inactive-color="#ff4949"
              :active-text="$t('text_enable')"
              :inactive-text="$t('text_disable')"
              @change="switchEnable($event as boolean, scope.row)"
            >
            </el-switch>
          </template>
          <template v-else>
            {{ scope.row[item.prop] || '--' }}
          </template>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="$t('btn_op')"
        width="200"
        align="center"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="editHandle(scope.row)"
          >{{ $t('btn_edit') }}</el-button>
          <el-button
            link
            type="primary"
            @click="editConfigHandle(scope.row)"
          >{{ $t('text_configure') }}</el-button>
        </template>
      </el-table-column>
    </ops-table>
  </div>
  <editTagLib v-if="tagEditVisible" v-model:visible="tagEditVisible" :edit-data="tagLibData" :lib-type="libType" @success="searchHandle" />
</template>

<script lang="ts">
import { tagLibList, tagLibEnable } from '@/api/tagLib'
import { useI18n } from 'vue-i18n'
import editTagLib from './editTagLib.vue'
import { useRouter } from 'vue-router'
export default defineComponent({
  name: 'TagLibraryConfig',
  components: {
    editTagLib,
    ElMessage
  }
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const _table = ref()
const searchTicketForm = ref({
  lib_name: '',
  lib_type: 1
})
const paramsTicket = computed(() => {
  return {
    ...searchTicketForm.value
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'lib_id', label: $t('text_tag_lib_id'), width: '120'},
    { prop: 'lib_name', label: $t('text_tag_lib_name') },
    { prop: 'projects', label: $t('text_linked_game') },
    { prop: 'op', label: $t('text_operator')},
    { prop: 'updated_at', label: $t('text_created_at') },
    { prop: 'enable', label: $t('text_status'), width: '100'}
  ]
})
const searchHandle = () => {
  _table.value.getData()
}

const tagEditVisible = ref(false)
const tagLibData = ref({})
const router = useRouter()
const libType = ref<number>()
const addTagLibHandle = () => {
  tagLibData.value = {}
  tagEditVisible.value = true
  libType.value = 1
}

const switchEnable = async (value: boolean, row: Record<string, unknown>) => {
  try {
    await tagLibEnable({
      object_id: row.lib_id,
      enable: value
    })
    ElMessage.success($t('text_status_success'))
    searchHandle()
  } catch (error) {
    console.log(error)
    row.enable = !value
  }
}
const editHandle = (row: Record<string, unknown>) => {
  tagLibData.value = row
  tagEditVisible.value = true
  libType.value = 1
}
const editConfigHandle = (row: Record<string, unknown>) => {
  router.push({
    path: 'editTagLibraryConfig',
    query: {
      lib_id: row.lib_id as number,
      lib_name: row.lib_name as string
    },
  })
}
</script>
<style lang="scss" scoped>
</style>