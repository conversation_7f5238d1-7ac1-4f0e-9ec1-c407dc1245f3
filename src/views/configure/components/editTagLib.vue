<template>
  <el-dialog v-model="value" width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('btn_edit_tag_base') : $t('btn_add_tag_base')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" size="small" :model="form" :rules="rules" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_tag_lib_name')" prop="lib_name">
        <el-input v-model="form.lib_name" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <el-form-item :label="$t('text_linked_game')" prop="projects">
        <el-select v-model="form.projects" :placeholder="$t('place_select')" multiple collapse-tags
          collapse-tags-tooltip clearable filterable :reserve-keyword="false">
          <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
            :value="v.game_project"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="请按照模板导入标签数据" prop="lib_file_url">
        <el-upload ref="upload" :action="`/gateway/proxy/cs-api/all/v1/upload_excel`" accept=".xls, .xlsx"
          :headers="{ 'admin-gateway-token': token }" :before-upload="handleBeforeUpload" :on-remove="handleRemove"
          :on-success="handleSuccess" :file-list="fileList" :on-error="handleFailed" :on-preview="handlePreview"
          :limit="1" class="upload_box">
          <el-button v-if="fileList.length > 0" type="primary" :disabled="true">点击上传</el-button>
          <el-button v-else type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              <i class="el-icon-warning-outline"></i> 只能上传excel文件
              <el-link type="primary" @click.prevent="downloadFile({
                filename: $t('text_tag_temp') + '.xlsx',
                url: enumList.DownloadUrl.filter((i: Record<string, string>) => i.name === 'TagLabDemo')[0].value
              })">下载模板</el-link>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules, UploadInstance, UploadFile } from 'element-plus'
import { genFileId } from 'element-plus'
import { saveTagLib } from '@/api/tagLib'
import { downloadFile } from '@/api/common'
import { useEnumStore, useUserInfoStore } from '@/stores'
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage
  }
})
interface EditTagLibProps {
  visible: boolean
  editData?: Record<string, unknown>
  libType: number
}
interface UploadSuccessRes {
  code: number
  data: {
    file_url: string,
    original_file_name: string
  }
  msg: string
}
interface Form {
  lib_name: string
  lib_file_url: string
  projects: string[],
  lib_id?: number,
  lib_type: number
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTagLibProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const gameList = computed(() => useEnumStore().gameList)
const enumList = computed(() => useEnumStore().enumList)
const token = computed(() => useUserInfoStore().userTocken)
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  lib_name: '',
  lib_file_url: '',
  projects: [],
  lib_type: props.libType
})
const rules = reactive<FormRules>({
  lib_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  lib_file_url: [{ required: true, message: $t('place_input'), trigger: 'change' }],
  projects: [{ required: true, message: $t('place_select'), trigger: 'change' }]
})
const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      await saveTagLib(form)
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// upload
const upload = ref<UploadInstance>()
const fileList = ref<UploadFile[]>([])
const handleBeforeUpload = (file: File) => {
  const fileType = file.name.substring(file.name.lastIndexOf(".") + 1)
  if (fileType !== 'xls' && fileType !== 'xlsx') {
    ElMessage.error('请上传excel文件！')
    return false
  }
}
const handleSuccess = ({ code, data, msg }: UploadSuccessRes) => {
  if (code !== 0) {
    ElMessage.error(msg)
    upload.value!.clearFiles()
    return
  }
  form.lib_file_url = data.file_url
  fileList.value.push({
    name: data.original_file_name,
    url: data.file_url,
    status: 'success',
    uid: genFileId()
  })
  ElMessage.success('上传成功！')
  formRef.value!.validateField('lib_file_url')
}
const handleFailed = () => {
  ElMessage.error('上传失败！')
}
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.indexOf(file)
  fileList.value.splice(index, 1)
  form.lib_file_url = ''
}
const handlePreview = (file: UploadFile) => {
  const a = document.createElement('a')
  const event = new MouseEvent('click')
  a.download = file.name
  a.href = file.url as string
  a.dispatchEvent(event)
}

// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.lib_id = props.editData.lib_id as number
    form.lib_name = props.editData.lib_name as string
    form.projects = props.editData.projects as string[]
    form.lib_file_url = props.editData.tag_upload_file as string
    if (form.lib_file_url) {
      fileList.value.push({
        name: form.lib_file_url.substring(form.lib_file_url.lastIndexOf('/') + 1),
        url: form.lib_file_url,
        status: 'success',
        uid: genFileId()
      })
    }
  }
})
</script>

<style scoped>
.upload_box {
  width: 100%;
}
</style>