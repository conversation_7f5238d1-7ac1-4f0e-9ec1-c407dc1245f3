<template>
  <el-dialog v-model="value" width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_tag') : $t('btn_add_tag')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" size="small" :rules="rules" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_tag_name')" prop="tag_name">
        <el-input v-model="form.tag_name" clearable :placeholder="$t('text_please_tag_name')" />
      </el-form-item>
      <el-form-item :label="$t('text_tag_desc')" prop="tag_desc">
        <el-input
          type="textarea"
          :rows="4"
          :placeholder="$t('text_please_tag_describe')"
          v-model.trim="form.tag_desc"
          maxlength="180"
          show-word-limit>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('text_isnot_enable')">
        <el-radio-group v-model="form.status" prop="status" @change="handleChange">
          <el-radio :value="1">{{ $t('text_yes') }}</el-radio>
          <el-radio :value="2">{{ $t('text_no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { saveDiscordTag, editDiscord } from '@/api/tagLib'
export default defineComponent({
  name: 'EditDiscordTag',
  components: {
    ElMessage
  }
})
interface EditDiscordTagProps {
  visible: boolean
  editData?: Record<string, unknown>
}
interface Form {
  tag_name: string
  tag_desc: string
  status: number
  id?: number
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditDiscordTagProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}

const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  tag_name: '',
  tag_desc: '',
  status: 1
})
const rules = reactive<FormRules>({
  tag_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
})

const handleChange = (val: number) => {
  form.status = val
}
const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      if (Object.keys(props.editData).length > 0) {
        await editDiscord(form)
      } else {
        await saveDiscordTag(form)
      }
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.id = props.editData.id as number
    form.tag_name = props.editData.tag_name as string
    form.tag_desc = props.editData.tag_desc as string
    form.status = props.editData.status as number
  }
})
</script>

<style scoped>
.upload_box {
  width: 100%;
}
</style>