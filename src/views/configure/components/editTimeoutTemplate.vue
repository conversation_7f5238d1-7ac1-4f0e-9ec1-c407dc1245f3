<template>
  <el-dialog v-model="value" width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_template') : $t('text_add_template')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" size="small" :model="form" :rules="rules" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_template_name')" prop="tpl_name">
        <el-input v-model="form.tpl_name" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <el-form-item :label="$t('text_linked_game')" prop="game_project">
        <el-select v-model="form.game_project" :placeholder="$t('place_select')" multiple collapse-tags
          collapse-tags-tooltip clearable>
          <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
            :value="v.game_project"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('text_tip_time')" prop="remind_time">
        <el-input-number v-model.number="form.remind_time" :step="1" step-strictly :max="2000" :min="0" />
      </el-form-item>
      <el-form-item :label="$t('text_template_content')" prop="content">
        <el-radio-group v-model="form.content_lang">
          <el-radio
            v-for="(v, index) in langList"
            :key="v.code + index"
            :label="v.name"
            :value="v.code"
          >{{v.name}}</el-radio>
        </el-radio-group>
        <el-input  v-model="form.content[form.content_lang]" type="textarea" :rows="4" :placeholder="$t('place_input')"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { editTempLib, addTempLib } from '@/api/timeoutTemplateConfig'
import { useEnumStore } from '@/stores'
export default defineComponent({
  name: 'EditTimeoutTemplate',
  components: {
    ElMessage
  }
})
interface EditTempLibProps {
  visible: boolean
  editData?: Record<string, unknown>
}
interface Form {
  tpl_name: string
  content: Record<string, string>
  game_project: string[]
  tpl_id?: number
  remind_time: number
  content_lang: string
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const gameList = computed(() => useEnumStore().gameList)
const langList = computed(() => useEnumStore().LangsList)
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  tpl_name: '',
  content: {
    en: ''
  },
  game_project: [],
  remind_time: 0,
  content_lang: 'en'
})
const rules = reactive<FormRules>({
  tpl_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  game_project: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  remind_time: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
})
const submit = () => {
  formRef.value!.validate(async (valid) => {
    console.log('valid', valid)
    if (!valid || loading.value ) return
    loading.value = true
    try {
      if (Object.keys(props.editData).length > 0) {
        await editTempLib(form)
      } else {
        await addTempLib(form)
      }
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.tpl_id = props.editData.id as number
    form.tpl_name = props.editData.tpl_name as string
    form.game_project = props.editData.game_project as string[]
    if (typeof props.editData.tpl_content === 'object' && props.editData.tpl_content !== null) {
      form.content = {...form.content, ...props.editData.tpl_content}
    }
    form.remind_time = props.editData.remind_time as number
  }
})
</script>

<style scoped>
.upload_box {
  width: 100%;
}
</style>