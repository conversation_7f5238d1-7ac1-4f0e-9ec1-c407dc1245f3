<template>
  <el-dialog v-model="value" width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_config') : $t('text_add_config')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" :rules="rules" ref="formRef" size="small" v-loading="loading">
      <el-form-item :label="$t('text_user_value')" prop="user">
        <el-select v-model="form.user" :disabled="Object.keys(props.editData).length > 0"
          :placeholder="$t('place_select')" multiple :reserve-keyword="false" filterable collapse-tags collapse-tags-tooltip clearable>
          <el-option v-for="(v, index) in csList" :key="index" :label="v.account" :value="v.account"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('text_lang')" prop="lang">
        <el-select v-model="form.lang" :placeholder="$t('place_select')" multiple collapse-tags
          collapse-tags-tooltip clearable>
          <template #header>
            <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAll">
              {{ $t('text_select_all') }}
            </el-checkbox>
          </template>
          <el-option v-for="(v, index) in langList" :key="index" :label="v.name" :value="v.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('text_upper_limit')" prop="upper_limit">
        <el-input-number v-model="form.upper_limit" :min="0" :max="50" :step="1" step-strictly
          :placeholder="$t('place_input')" clearable />
      </el-form-item>
      <el-form-item>
				<div class="button-group">
					<el-button type="primary" plain @click="addSkill">{{ $t('text_adds') }}</el-button>
					<el-button type="primary" plain @click="deleteSkill">{{ $t('text_delete') }}</el-button>
				</div>
				<el-table :data="form.detail" @selection-change="handleSelectionChange">
					<el-table-column type="selection"></el-table-column>
					<el-table-column :label="$t('text_game')">
						<template #default="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.game'" :rules="rules.game">
                <el-select
                  filterable
                  v-model="scope.row.game"
                  :placeholder="$t('place_select')"
                  @change="projectChange($event, scope.row.indexId)"
                  style="width: 166px;"
                >
                  <el-option
                    v-for="(game, indexG) in gameList"
                    :key="indexG"
                    :label="game.app_name"
                    :value="game.game_project"
                  ></el-option>
                </el-select>
              </el-form-item>
						</template>
					</el-table-column>
					<el-table-column :label="$t('text_qtype')">
						<template #default="scope">
							<el-cascader
								v-model="scope.row.categories"
								:options="scope.row.questionList"
								:props="{ multiple: true, value: 'id' }"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
								clearable
							>
								<template #default="{ node, data }">
									<span>{{ data.label }}</span>
								</template>
							</el-cascader>
						</template>
					</el-table-column>
          <el-table-column :label="$t('提单渠道')">
            <template #default="scope">
              <el-form-item>
                <el-select
                  v-model="scope.row.system_tag"
                  :placeholder="$t('place_select')"
                  multiple collapse-tags
                  collapse-tags-tooltip
                  style="width: 166px;"
                >
                  <el-option v-for="(v, index) in enumList.TicketSystemTag" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
						</template>
          </el-table-column>
				</el-table>
			</el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useEnumStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { assignConfigSave, assignConfigEdit, getAcceptorList, getQuestionList } from '@/api/assignConfig'
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage
  }
})
interface EditTagLibProps {
  visible: boolean
  editData?: Record<string, unknown>
}
interface Form {
  id?: number
  account: string
  operator: string
  user: any[]
  detail: any[]
  lang: string[]
  upper_limit: number
}
</script>
<script setup lang="ts">
// base
const enumList = computed(() => useEnumStore().enumList)
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTagLibProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const checkAll = ref(false)
const indeterminate = ref(false)
const csList = ref([])
const getCsList = async () => {
  const res = await getAcceptorList({})
  csList.value = res
}
getCsList()
const loading = ref(false)
const gameList = computed(() => useEnumStore().gameList)
const langList = computed(() => useEnumStore().LangsList)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  account: '',
  operator: '',
  user: [],
  lang: [],
  detail: [],
  upper_limit: 0
})
const indexId = ref(0)
const multipleSelection = ref<any[]>([])
watch(() => form.lang, (val) => {
  if (val.length === 0) {
    checkAll.value = false
    indeterminate.value = false
  } else if (val.length === langList.value.length) {
    checkAll.value = true
    indeterminate.value = false
  } else {
    indeterminate.value = true
  }
})
// 语言全选功能
const handleCheckAll = (val: boolean) => {
  indeterminate.value = false
  if (val) {
    form.lang = langList.value.map((_) => _.code)
  } else {
    form.lang = []
  }
}

const rules = reactive<FormRules>({
  user: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  lang: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  upper_limit: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  game: [{ required: true, message: $t('place_select'), trigger: 'change' }]
})
const handleSelectionChange = (val: any) => {
  multipleSelection.value = val
}
const addSkill = () => {
  form.detail.push({
    indexId: indexId.value,
    game: '',
    language: '',
    categories: [],
    questionList: [],
    system_tag: []
  })
  indexId.value++
}
addSkill()
const deleteSkill = () => {
  multipleSelection.value.forEach((select: any) => {
    form.detail.forEach((item, index) => {
      if (item.indexId === select.indexId) {
        form.detail.splice(index, 1)
      }
    })
  })
}
const projectChange = (val: string, indexId: number) => {
  form.detail.forEach(item => {
    item.indexId === indexId && (item.categories = [])
  })
  handleQuestion(val, indexId)
}
// 根据游戏获取下面的问题分类
const handleQuestion = (project: string, indexId: number) => {
  getQuestionList({project: project}).then((res: any) => {
    form.detail.forEach((item) => {
      if(item.indexId === indexId) {
        item.questionList = res
      }
    })
  })
}
const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value) return
    loading.value = true
    try {
      const params = { ...form }
      params.detail.forEach((item) => {
        item.categories = item.categories.flat().map(String)
      })
      const method = form.id ? assignConfigEdit : assignConfigSave
      await method(params)
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.id = props.editData.id as number
    form.user = [props.editData.account]
    form.lang = props.editData.lang as string[]
    form.detail = props.editData.detail as any[]
    if (form.detail) {
      form.detail.forEach((item) => {
        item.indexId = indexId.value
        handleQuestion(item.game, item.indexId)
        item.categories = item.categories.map(Number)
        indexId.value++
      })
    }
    form.upper_limit = props.editData.upper_limit as number
    form.account = props.editData.account as string
    form.operator = props.editData.operator as string
    
  }
})
</script>

<style lang='scss' scoped>
.button-group {
  margin-bottom: 10px;
}
.el-dialog {
  .el-form  {
    &:deep(.el-form-item) {
      margin: 20px 0!important;
    }
  }
}
</style>