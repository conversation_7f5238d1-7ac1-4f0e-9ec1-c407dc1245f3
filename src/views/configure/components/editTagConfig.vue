<template>
  <el-dialog
    v-model="value"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_tag') : $t('text_add_tag')"
    width="450px"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form size="small" :model="form" :rules="rules" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_tag_name')" prop="tag_name">
        <el-input v-model="form.tag_name" clearable :placeholder="$t('place_input')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('btn_ok') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { configTagAdd, configTagEdit } from '@/api/tagLib'
export default defineComponent({
  name: 'EditTagConfig',
  components: {
    ElMessage
  }
})
interface EditTagLibProps {
  visible: boolean
  editData?: Record<string, unknown>
}
interface Form {
  lib_id?: number
  level?: number
  pid?: number
  tag_id?: number
  tag_name: string
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTagLibProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}

const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  tag_name: '',
})

const rules = reactive<FormRules>({
  tag_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
})

const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value) return
    loading.value = true
    try {
      if(form.tag_id) {
        await configTagEdit({
          tag_id: form.tag_id,
          tag_name: form.tag_name
        })
        ElMessage.success($t('text_edit_success'))
      } else {
        await configTagAdd({
          lib_id: form.lib_id,
          level: form.level,
          pid: form.pid,
          tag_name: form.tag_name
        })
        ElMessage.success($t('text_add_success'))
      }
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.lib_id = props.editData.lib_id as number
    form.level = props.editData.level as number
    form.tag_id = props.editData.tag_id as number
    form.pid = props.editData.pid as number
    form.tag_name = props.editData.tag_name as string
  }
})
</script>

<style scoped>

</style>