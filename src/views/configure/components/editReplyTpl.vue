<template>
  <el-dialog v-model="value" width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_reply_template') : $t('btn_add_reply_template')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" size="small" :rules="rules" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_reply_template_name')" prop="tpl">
        <el-input v-model="form.tpl" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <el-form-item v-for="(v, k) in langList" :key="k" :label="v.name + $t('text_reply_content') + '：'" :prop="'reply_content.' + v.code">
				<opsEditer v-model="form.reply_content[v.code]"></opsEditer>
			</el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import { useEnumStore } from '@/stores'
import type { FormInstance, FormRules } from 'element-plus'
import { addReplyTemplate, editReplyTemplate, replyTemplateInfo } from '@/api/templateLib'
export default defineComponent({
  name: 'editReplyTpl',
  components: {
    ElMessage
  }
})
interface EditDiscordTagProps {
  visible: boolean
  editData?: Record<string, unknown>
}
interface Form {
  tpl: string
  reply_content: Record<string, string>
}
</script>
<script setup lang="ts">
const langList = computed(() => useEnumStore().LangsList)
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditDiscordTagProps>(), {
  visible: false,
  editData: () => ({})
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}

const loading = ref(false)
const formRef = ref<FormInstance>()
let form = ref<Form>({
  tpl: '',
  reply_content: {}
})
const rules = reactive<FormRules>({
  tpl: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  reply_content: {}
})

const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      if (Object.keys(props.editData).length > 0) {
        await editReplyTemplate(form.value)
      } else {
        await addReplyTemplate(form.value)
      }
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    replyTemplateInfo({
      tpl_id: props.editData.id as number
    }).then((res: any) => {
      form.value = res
    })
  }
  langList.value.forEach(item => {
    rules.reply_content[item.code] = [{ required: true, message: $t('place_input'), trigger: 'blur' }]
  })
})
</script>

<style scoped lang="scss">

</style>