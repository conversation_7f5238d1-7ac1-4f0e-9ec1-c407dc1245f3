<template>
  <el-dialog
    :title="$t('text_train_mode')"
    :model-value="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeHandle"
    width="600px"
  >
    <!-- 新增 查看训练结果按钮，点击跳转到训练结果页面 -->
    <el-button class="view-training-results-btn" type="text" @click="viewTrainingResults">{{
      $t('text_view_training_results')
    }}</el-button>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" size="small">
      <el-form-item :label="`${$t('text_game')}：`" prop="project">
        <el-select
          v-model="form.project"
          :placeholder="$t('place_select') + $t('text_game')"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t('text_lang')}：`" prop="lang">
        <el-radio-group v-model="form.is_all">
          <el-radio :label="true">{{ $t('text_train_all_languages') }}</el-radio>
          <el-radio :label="false">{{ $t('text_train_selected_languages') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!form.is_all">
        <el-select
          v-model="form.lang"
          :placeholder="$t('place_select')"
          clearable
          filterable
          multiple
          style="width: 100%"
        >
          <el-option
            v-for="(v, index) in langList"
            :key="index"
            :label="v.name"
            :value="v.code"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeHandle">{{ $t('btn_cancel') }}</el-button>
        <el-button type="primary" @click="submitHandle" :loading="loading">{{
          $t('text_confirm')
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useI18n } from 'vue-i18n';
// @ts-ignore
import { useEnumStore } from '@/stores';
// @ts-ignore
import { trainCorpus } from '@/api/ticketCorpus';
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: 'TrainCorpus',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible', 'success'],
});
</script>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits(['update:visible', 'success']);
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const langList = computed(() => useEnumStore().LangsList);

// 语言选项
const languageOptions = ref([
  { label: $t('text_simplified_chinese'), value: 'zh-CN' },
  { label: $t('text_traditional_chinese'), value: 'zh-TW' },
  { label: $t('text_english'), value: 'en' },
  { label: $t('text_japanese'), value: 'ja' },
  { label: $t('text_korean'), value: 'ko' },
]);

const formRef = ref<FormInstance>();
const form = ref({
  project: '',
  is_all: true,
  lang: [] as string[],
});

const rules = ref<FormRules>({
  project: [
    { required: true, message: $t('text_please_select') + $t('text_game'), trigger: 'change' },
  ],
  lang: [
    {
      required: true,
      message: $t('text_please_select') + $t('text_lang'),
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!form.value.is_all && (!value || value.length === 0)) {
          callback(new Error($t('text_please_select') + $t('text_lang')));
        } else {
          callback();
        }
      },
    },
  ],
});

const loading = ref(false);

const viewTrainingResults = () => {
  router.push({
    path: '/configure/trainingResults',
  });
};

const closeHandle = () => {
  form.value = {
    project: '',
    is_all: true,
    lang: [],
  };
  emit('update:visible', false);
};

const submitHandle = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        loading.value = true;

        const params = {
          project: form.value.project,
          is_all: form.value.is_all,
          lang: form.value.is_all ? [] : form.value.lang,
        };

        await trainCorpus(params);
        ElMessage.success($t('text_training_success'));
        emit('success');
        closeHandle();
      } catch (error) {
        console.error(error);
        ElMessage.error($t('text_training_failed'));
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.view-training-results-btn {
  position: absolute;
  left: 120px;
  top: 13px;
  font-size: 18px;
  line-height: 24px;
  cursor: pointer;
}
</style>
