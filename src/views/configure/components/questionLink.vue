<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_question_link')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <div class="link-centent" v-loading="loading">
      <el-link :disabled="linkUrl === ''" :href="linkUrl" type="primary" target="_blank">{{
        linkUrl
      }}</el-link>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { questionnaireConfigCreateUrl } from '@/api/questionnaireConfig';
export default defineComponent({
  name: 'EditDiscordTag',
  components: {
    ElMessage,
  },
});
interface LinkProps {
  visible: boolean;
  linkData?: Record<string, unknown>;
}
</script>
<script setup lang="ts">
// base
const props = withDefaults(defineProps<LinkProps>(), {
  visible: false,
  linkData: () => ({}),
});
const linkUrl = ref('');
const loading = ref(false);
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};

// edit
onMounted(() => {
  if (Object.keys(props.linkData).length > 0) {
    loading.value = true;
    questionnaireConfigCreateUrl({
      project: props.linkData.project,
      survey_id: props.linkData.id,
    })
      .then((res: any) => {
        linkUrl.value = res.link;
      })
      .finally(() => {
        loading.value = false;
      });
  }
});
</script>

<style lang="scss" scoped>
.upload_box {
  width: 100%;
}
.link-centent {
  padding: 20px;
  word-break: break-all;
}
</style>
