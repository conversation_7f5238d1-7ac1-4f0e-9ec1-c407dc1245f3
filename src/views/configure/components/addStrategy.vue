<template>
  <el-dialog
    :title="editData ? $t('text_strategy_edit') : $t('text_strategy_add')"
    v-model="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <el-form :model="form" ref="formRef" :rules="rules" label-width="120px">
      <el-form-item :label="$t('text_strategy_name')" prop="strategy_name">
        <el-input
          v-model="form.strategy_name"
          :placeholder="$t('text_strategy_name_placeholder')"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('text_game')" prop="project">
        <el-select
          v-model="form.project"
          :placeholder="$t('place_select') + $t('text_game')"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('text_filter_server_lists')" prop="type">
        <el-radio-group v-model="form.type" @change="handleServerTypeChange">
          <el-radio :label="1">{{ $t('text_all') }}</el-radio>
          <el-radio :label="2">{{ $t('text_part') }}</el-radio>
        </el-radio-group>
        <el-input
          v-if="form.type === 2"
          v-model="form.filter_server_lists"
          :placeholder="$t('text_sid_place')"
          style="margin-top: 10px"
        ></el-input>
      </el-form-item>
      <div class="condition-box">
        <div class="condition-title">{{ $t('text_ai_processing_conditions') }}</div>
        <el-form-item :label="$t('text_filter_pay_range_lists')" prop="filter_pay_range_lists">
          <el-input
            v-model="form.filter_pay_range_lists"
            :placeholder="$t('text_filter_pay_range_lists_placeholder')"
          >
          </el-input>
        </el-form-item>
        <!-- <el-form-item :label="$t('text_filter_castle_level_lists')" prop="filter_castle_level_lists">
          <el-input
            v-model="form.filter_castle_level_lists"
            type="number"
            :min="0"
            :placeholder="$t('text_filter_castle_level_lists_placeholder')"
          ></el-input>
        </el-form-item> -->
      </div>
    </el-form>
    <div class="condition-description">
      {{ $t('text_ai_condition_description') }}
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ $t('btn_cancel') }}</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">{{ $t('text_confirm') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, FormInstance } from 'element-plus'
// @ts-ignore
import { useEnumStore } from '@/stores'
import { computed } from 'vue'
import { strategySave } from '@/api/strategy'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  editData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

const { t: $t } = useI18n()
const gameList = computed(() => useEnumStore().gameList)

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const form = reactive({
  strategy_id: null,
  project: '',
  strategy_name: '',
  type: 1,
  filter_server_lists: '',
  filter_pay_range_lists: '',
  filter_castle_level_lists: '',
  enable: 0
})

const rules = reactive({
  project: [{ required: true, message: $t('text_project_placeholder'), trigger: 'change' }],
  strategy_name: [{ required: true, message: $t('text_strategy_name_placeholder'), trigger: 'blur' }, { max: 100, message: $t('text_strategy_name_max_length'), trigger: 'blur' }],
  type: [{ required: true, message: $t('text_filter_server_lists_placeholder'), trigger: 'change',
    validator: (rule, value, callback) => {
      if (value === 2 && !form.filter_server_lists) {
        callback(new Error($t('text_filter_server_lists_placeholder')))
      } else {
        callback()
      }
    }
   }],
  filter_pay_range_lists: [{ required: true, message: $t('text_pay_range_invalid'), trigger: 'blur' }],
  enable: [{ required: true, message: $t('text_strategy_status'), trigger: 'change' }]
})

const formRef = ref<FormInstance>()
const loading = ref(false)

watch(() => props.editData, (val) => {
  if (val) {
    Object.keys(form).forEach(key => {
      if (key in val) {
        form[key] = val[key]
      }
    })

    if (val.filter_server_lists) {
      form.type = 2
    } else {
      form.type = 1
    }
  } else {
    resetForm()
  }
}, { immediate: true, deep: true })

const handleServerTypeChange = (val: number) => {
  if (val === 1) {
    form.filter_server_lists = ''
  }
}

const resetForm = () => {
  form.strategy_id = null
  form.project = ''
  form.strategy_name = ''
  form.type = 1
  form.filter_server_lists = ''
  form.filter_pay_range_lists = ''
  form.filter_castle_level_lists = ''
  form.enable = 0
}

const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true
    try {
      const submitData = {
        ...form,
        filter_server_lists: form.type === 1 ? '' : form.filter_server_lists,
        filter_pay_range_lists: form.filter_pay_range_lists ? form.filter_pay_range_lists.toString() : '',
        filter_castle_level_lists: form.filter_castle_level_lists ? form.filter_castle_level_lists.toString() : ''
      }

      await strategySave(submitData)

      ElMessage.success($t('text_operate_success'))
      dialogVisible.value = false
      emit('success')
    } catch (error) {
      console.error(error)
      ElMessage.error($t('text_operate_failed'))
    } finally {
      loading.value = false
    }
  })
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.condition-box {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.condition-title {
  font-weight: bold;
  margin-bottom: 15px;
}

.condition-description {
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
  line-height: 1.5;
}
</style>
