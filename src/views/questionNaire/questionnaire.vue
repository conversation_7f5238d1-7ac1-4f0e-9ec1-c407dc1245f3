<template>
  <div class="q-box">
    <div class="q-container">
      <div class="select-lang">
        <el-select
          v-model="lang"
          :placeholder="$t('place_select')"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="(v, index) in langList"
            :key="index"
            :label="v.code"
            :value="v.code"
          ></el-option>
        </el-select>
      </div>
      <div class="q-title">
        <span class="h-title">2024年游戏科技客户满意度调查表</span>
      </div>
      <div class="q-content">
        <div class="q-custonize-dsc">
          感谢您在百忙之中抽空填写此调查表，为了更好的服务贵司，请您提出宝贵建议，我们将及时改善，谢谢您！
        </div>
        <div class="dotted-line"></div>
        <div class="q-body">
          <span class="q-pub q-des">您是否愿意将我司推介给有需求的朋友或公司？</span>
          <span class="q-tips">「5星为非常满意,1~3星为不满意」</span>
          <div class="q-rate">
            <el-rate
              v-model="rateVal"
              :texts="['1星', '2星', '3星', '4星', '5星']"
              show-text
              size="large"
              @change="handleRateVal"
            />
          </div>
          <!-- 1~3星不满意时，追问 -->
          <div class="q-probe" v-if="rateVal === 1 || rateVal === 2 || rateVal === 3">
            <span class="q-pub q-des"
              >很抱歉给您带来了不好的体验，烦请您简述下当时遇到的问题，方便我们优化改进。</span
            >
            <el-form class="form" :model="form" :rules="rules" ref="formRef">
              <el-form-item prop="reply">
                <el-input type="text" :placeholder="$t('place_input')" v-model.trim="form.reply" />
              </el-form-item>
            </el-form>
          </div>
          <div class="q-probe" v-if="rateVal === 1 || rateVal === 2 || rateVal === 3">
            <span class="q-pub q-des">【必填】我们欢迎您留下您的游戏UID，帮助我们进行改进。</span>
            <el-form class="form" :model="form" :rules="rules" ref="formRef">
              <el-form-item prop="uid">
                <el-input type="text" :placeholder="$t('place_input')" v-model.trim="form.uid" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <div class="q-submit">
        <el-button type="primary" icon="Select">提交问卷</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { useEnumStore } from '@/stores';
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
// import { useRoute, useRouter } from 'vue-router'
// import { appraiseTicket } from '@/api/tickets'
export default defineComponent({
  name: 'QuestionNaire',
  components: {
    ElMessage,
  },
});
interface Form {
  reply: string;
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const rateVal = ref();
const langList = computed(() => useEnumStore().LangsList);
const lang = ref('');
const form = reactive<Form>({
  reply: '',
});
const rules = reactive<FormRules>({
  reply: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
});
const handleRateVal = (val: number) => {
  rateVal.value = val;
  console.log(val, '评分----');
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.q-box {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #ebedf0;
  padding-left: 18%;
  padding-right: 18%;

  .q-container {
    background: #fff;
    min-height: 100%;
    padding: 20px 70px 0;
    .select-lang {
      text-align: right;
    }
    .q-title {
      background-color: rgba(255, 255, 255, 0.9);
      padding: 26px 10px 12px;
      width: 100%;
      color: #1ea0fa;
      text-align: center;

      .h-title {
        font-size: 20px;
        line-height: 32px;
        font-weight: bold;
        margin: 0;
        padding: 0;
      }
    }
  }

  .q-content {
    padding: 0 18px;

    .q-custonize-dsc {
      font-size: 16px;
      color: #666;
      text-indent: 25px;
      line-height: 24px;
      margin: 16px 0;
    }

    .dotted-line {
      border-top: 1px dashed #c2c2c2;
      margin-bottom: 16px;
    }

    .q-pub {
      display: block;
      padding-bottom: 5px;

      &:before {
        margin-right: 5px;
        color: red;
        content: '*';
        display: inline;
      }
    }

    .q-tips {
      display: block;
      color: #666;
      font-size: 14px;
      padding-bottom: 5px;
    }

    .q-des {
      font-size: 16px;
      font-weight: bold;
      line-height: 26px;
    }
  }
  .q-submit {
    padding: 20px 0;
    width: 100%;
    text-align: center;
  }
}
/* 针对屏幕宽度在480像素到768像素之间的设备 */
@media (min-width: 480px) and (max-width: 768px) {
  .q-box {
    padding: 0 5%;
    .q-container {
      padding: 20px 70px 0;
    }
  }
}
/* 针对屏幕宽度小于480像素的设备 */
@media (max-width: 480px) {
  .q-box {
    padding: 0 5%;
    .q-container {
      padding: 10px 10px 0;
    }
  }
}
// 兼容ipad竖屏
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: portrait) {
  .q-box {
    padding: 0 10%;
    .q-container {
      padding: 20px 20px 0;
    }
  }
}
</style>
