<template>
  <div class="item-wapper">
    <p class="utc-time">
      <span>UTC</span> {{ props.itemData.created_at }} <span>{{ dialogItemName }}</span>
    </p>
    <!-- 玩家内容：表单 -->
    <div v-if="props.itemData.is_ticket === 1" class="dl-player-form">
      <el-descriptions size="small" class="ticket" :column="1" direction="horizontal">
        <el-descriptions-item
          v-for="(v, k) in JSON.parse(props.itemData.detail)"
          :label="k"
          :key="k"
          label-class-name="form-label"
        >
          <template v-if="Array.isArray(v)">
            <div v-if="v.length && v[0].file_type === 'image'" class="img-wrap">
              <div class="img-wrap-item" v-for="(img, index) in v" :key="index">
                <template v-if="!img.url.includes('https://kg-web-cdn')">
                  {{ img.url }}
                </template>
                <template v-else-if="img.url.split(',').length > 0">
                  <el-image
                    v-for="(img, index) in img.url.split(',')"
                    :key="index"
                    :src="img"
                    alt=""
                    :preview-src-list="getPreviewList(index, v)"
                    fit="cover"
                  ></el-image>
                </template>
                <el-image
                  v-else
                  :src="img.url"
                  alt=""
                  :preview-src-list="getPreviewList(index, v)"
                  fit="cover"
                ></el-image>
              </div>
            </div>
            <div v-if="v.length && v[0].file_type === 'video'" class="video-wrap">
              <opsVideo
                :video-url="v[0].url"
                class="video-player-box"
                :id="props.itemData.created_at + props.itemData.operator"
              ></opsVideo>
              <!-- <i class="el-icon-download download" @click="downloadVideo(v[0].url, v[0].file_name)"></i> -->
            </div>
          </template>
          <template v-else><span v-html="v"></span></template>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 玩家内容：重开的表单 -->
    <div v-if="props.itemData.is_ticket === 2" class="dl-player-form">
      <el-descriptions size="small" class="ticket" :column="1" direction="horizontal">
        <el-descriptions-item label="重开单诉求：" label-class-name="form-label">{{
          props.itemData.detail
        }}</el-descriptions-item>
        <el-descriptions-item label="图片/视频：" label-class-name="form-label">
          <template
            v-if="
              Array.isArray(JSON.parse(props.itemData.files as string)) &&
              JSON.parse(props.itemData.files as string).length > 0
            "
            v-for="(v, k) in JSON.parse(props.itemData.files as string)"
            :key="k"
          >
            <div v-if="v.file_type === 'image'" class="img-wrap">
              <el-image
                hide-on-click-modal
                :src="v.url"
                alt=""
                :preview-src-list="getPreviewList(0, [v])"
                fit="cover"
              ></el-image>
            </div>
            <div v-if="v.file_type === 'video'" class="video-wrap">
              <opsVideo
                :video-url="v.url"
                class="video-player-box"
                :id="props.itemData.created_at + props.itemData.operator"
              ></opsVideo>
            </div>
          </template>
          <template v-else>
            {{ $t('text_none') }}
          </template>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 玩家内容：对话 -->
    <div
      v-if="
        props.itemData.is_ticket === 0 &&
        props.itemData.from_role === 1 &&
        props.itemData.commu_type === 'CommuTypeDialogue'
      "
      class="dl-player"
    >
      <div class="image_preview">
        <!-- 图片预览 -->
        <el-image-viewer
          hide-on-click-modal
          @close="
            () => {
              showViewer = false;
            }
          "
          v-if="showViewer"
          :url-list="previewList"
        />
      </div>
      <div @click="getImg($event)">
        <p v-html="props.itemData.detail" class="richtext-box"></p>
      </div>
      <div v-if="props.itemData.picture" class="img-wrap">
        <el-image
          hide-on-click-modal
          v-for="(item, index) in props.itemData.picture.split(',')"
          :key="index"
          :src="item"
          alt=""
          :preview-src-list="getPlayerPreviewList(item)"
          fit="cover"
        ></el-image>
      </div>
      <div v-if="props.itemData.video" class="video-wrap">
        <opsVideo
          v-for="(item, index) in props.itemData.video.split(',')"
          :key="index"
          :video-url="item"
          :id="item + index"
          class="video-player-box"
        ></opsVideo>
      </div>
    </div>
    <!-- 客服内容 -->
    <div
      v-if="props.itemData.from_role !== 1 && props.itemData.commu_type === 'CommuTypeDialogue'"
      class="dl-cs"
    >
      <div class="image_preview">
        <!-- 图片预览 -->
        <el-image-viewer
          hide-on-click-modal
          @close="
            () => {
              showViewer = false;
            }
          "
          v-if="showViewer"
          :url-list="previewList"
        />
      </div>
      <div @click="getImg($event)">
        <div v-html="props.itemData.detail" class="richtext-box"></div>
      </div>
    </div>
    <!-- 备注内容 -->
    <div v-if="props.itemData.commu_type === 'CommuTypeRemark'" class="dl-remark">
      <div class="image_preview">
        <!-- 图片预览 -->
        <el-image-viewer
          hide-on-click-modal
          @close="
            () => {
              showViewer = false;
            }
          "
          v-if="showViewer"
          :url-list="previewList"
        />
      </div>
      <div @click="getImg($event)">
        <div v-html="props.itemData.detail" class="richtext-box"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
export default defineComponent({
  name: 'DialogItem',
  components: {
    ElMessage,
  },
});
interface DialogItem {
  commu_type: 'CommuTypeDialogue' | 'CommuTypeRemark'; // CommuTypeDialogue:对话消息  CommuTypeRemark:备注消息
  created_at: string;
  detail: string;
  from_role: 1 | 2 | 3; // 1:玩家 2:客服 3:系统
  is_ticket: 0 | 1 | 2; // 0:非工单 1:工单 2:重开工单
  operator: string;
  files?: string;
  video: string;
  picture: string;
}
interface DialogItemProps {
  itemData: DialogItem;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = defineProps<DialogItemProps>();
const showViewer = ref<boolean>(false);
const previewList = ref<string[]>([]);

// console.log('props.itemData', props.itemData)
// business
const dialogItemName = computed(() => {
  let name = '';
  if (props.itemData.from_role === 1) {
    name = '【' + $t('text_player') + '】';
  } else {
    name = props.itemData.operator ? '【' + props.itemData.operator + '】' : '';
  }
  return name;
});
const getPreviewList = (
  index: number,
  imgList: {
    file_name: string;
    file_type: string;
    url: string;
  }[]
): string[] => {
  const newArr = imgList.slice(index).concat(imgList.slice(0, index));
  const res: string[] = [];
  newArr.forEach(img => res.push(img.url));
  return res;
};
const getPlayerPreviewList = (url: string) => {
  const res: string[] = [];
  res.push(url);
  return res;
};
const getImg = ($event: MouseEvent) => {
  const target = $event.target as HTMLImageElement | null;
  // 确保事件目标本身是图片
  if (target && target instanceof HTMLImageElement && target.tagName === 'IMG' && target.src) {
    previewList.value = [target.src];
    showViewer.value = true;
  }
};
</script>
<style lang="scss" scoped>
.item-wapper {
  overflow: hidden;
  height: auto;
  padding: 0px 10px;
  margin-bottom: 20px;
  > div {
    margin: 5px 0px 15px;
    padding: 6px;
    border: 1px solid #ebeef5;
    border-radius: 5px;
    box-sizing: border-box;
    width: 80%;
    background-color: #f8f9fa;
    font-size: 12px;
  }
  .utc-time {
    text-align: center;
    font-size: 12px;
    color: #999;
    margin: 0 auto;
    span {
      margin-right: 5px;
    }
  }
  .dl-player-form {
    float: left;
    .ticket {
      cursor: auto !important;
      width: 100% !important;
      margin: 0 !important;
      padding: 0px !important;
      border-radius: 0px !important;

      &:deep(.el-descriptions__body) {
        background-color: #f8f9fa !important;
      }
      &:deep(.el-descriptions__cell) {
        display: flex;
        .el-descriptions__label {
          max-width: 50%;
        }
      }
    }
  }
  .dl-player {
    float: left;
  }
  .dl-cs {
    float: right;
  }
  .dl-remark {
    width: 100%;
    max-width: 100%;
    background-color: #edf6f2;
  }
  .img-wrap {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .img-wrap-item {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
    }
    .el-image {
      width: 80px;
      height: 80px;
      display: block;
      margin: 0 8px 8px 0;
    }
  }
  .video-wrap {
    width: 100%;
    height: 200px;
    display: flex;
    .video-player-box {
      width: 100%;
      height: 100%;
    }
    .download {
      font-size: 18px;
      margin: 180px 0 0 10px;
      color: #49a3a5;
      cursor: pointer;
    }
  }
  .richtext-box {
    & ::v-deep(img) {
      max-width: 100%;
      height: auto;
    }
  }
}
</style>
