<template>
  <el-dialog v-model="value" width="800px"
  :title="props.opType === 1 ? $t('text_batch_reply') : $t('text_batch_reply_customs_declaration')"
  :destroy-on-close="true"
  :before-close="close"
  :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <el-form-item>
        <opsEditer v-model="form.content" v-model:uploading="isUploading"></opsEditer>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" :disabled="form.content === ''" @click="submit">{{ $t('text_confirm')
        }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { ticketBatchReply } from '@/api/overview'
export default defineComponent({
  name: 'BatchReply',
  components: {
    ElMessage
  }
})
interface ReplyProps {
  visible: boolean
  ticketIds: number[]
  opType?: number
}
interface Form {
  content: string
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<ReplyProps>(), {
  visible: false,
  ticketIds: () => [],
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const isUploading = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  content: ''
})

const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value) return
    loading.value = true
    try {
      await ticketBatchReply({content: form.content, ticket_ids: props.ticketIds, op_type: props.opType})
      ElMessage.success($t('text_batch_operate_success'))
      emit('success')
      value.value = false
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
onMounted(() => {
  // form.ticket_id = props.ticketId
})
</script>