<template>
  <el-dialog v-model="value" width="800px" :title="$t('btn_add_remark')" :destroy-on-close="true" :before-close="close"
    :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <el-form-item>
        <opsEditer v-model="form.content" v-model:uploading="isUploading"></opsEditer>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" :disabled="form.content === '' || isUploading" @click="submit">{{ $t('text_confirm')
        }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { addRemark, draftSave, draftInfo } from '@/api/overview'
export default defineComponent({
  name: 'OverviewRemark',
  components: {
    ElMessage
  }
})
interface AssignProps {
  visible: boolean
  ticketId: number
}
interface Form {
  id?: number
  ticket_id?: number
  content: string
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false,
  ticketId: 0
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
  if(form.content) {
    draftSave(form).then(() => {
      ElMessage.success($t('text_draft_save_success'))
    })
  }
}
// business
const isUploading = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  content: ''
})

const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value) return
    loading.value = true
    try {
      await addRemark(form)
      ElMessage.success($t('text_success'))
      emit('success')
      value.value = false
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
onMounted(() => {
  form.ticket_id = props.ticketId
  draftInfo({ticket_id: form.ticket_id}).then(((res: any) => {
    form.content = res.content
    form.id = res.id
  }))
})
</script>