<template>
  <el-dialog v-model="value" width="600px" :title="$t('btn_reply_close')" :destroy-on-close="true" :before-close="close"
    :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form">
      <el-form-item :label="$t('text_reply_content')">
        <div v-html="replyContent" class="reply-content"></div>
      </el-form-item>
      <div v-if="form.question || form.answer">
        <el-form-item :label="$t('text_question')">
          <el-input type="textarea" :rows="4" v-model="form.question" />
        </el-form-item>
        <el-form-item :label="$t('text_answer')">
          <el-input type="textarea" :rows="6" v-model="form.answer" />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submit">{{ $t('btn_confirm_send') }}</el-button>
      <el-checkbox v-if="form.question || form.answer" v-model="form.is_sync_ai_elfin" :label="$t('text_sync_data')" size="large" style="margin-left: 10px;" />
      <el-button v-else type="primary" @click="submitQaData" v-loading="qaDataLoading">{{ $t('btn_qa_data') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { aiFaq } from '@/api/overview'
import { useI18n } from 'vue-i18n'
const { t: $t } = useI18n()
interface AssignProps {
  visible: boolean
  ticketId: number
  replyContent: string
}
interface Form {
  question: string
  answer: string
  is_sync_ai_elfin: boolean
}
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false,
  ticketId: 0,
  replyContent: ''
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'sync-data', num: number, formInfo: Record<string, unknown>): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}

const form = reactive<Form>({
  question: '',
  answer: '',
  is_sync_ai_elfin: false
})
const submit = () => {
  emit('sync-data', 2, { ...form })
  close()
}
const qaDataLoading = ref<boolean>(false)
const submitQaData = () => {
  qaDataLoading.value = true
  aiFaq({
    ticket_id: props.ticketId,
    content: props.replyContent
  }).then((res: any) => {
    form.question = res.question
    form.answer = res.answer
    form.is_sync_ai_elfin = true
  }).finally(() => qaDataLoading.value = false)
}
</script>

<style lang="scss" scoped>
.reply-content {
  border: 1px solid #dcdfe6;
  max-height: 260px;
  width: 100%;
  overflow: auto;
  padding: 0 10px;
}
</style>