<template>
  <el-dialog v-model="value" width="600px" :title="$t('text_edit_tag')" :destroy-on-close="true" :before-close="close"
    :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <el-form-item prop="label_id">
        <el-cascader v-model="form.label_id" style="width:100%" :options="tagOpts" filterable
          :placeholder="$t('text_tag_placeholder')" :reserve-keyword="false"
          :props="{ multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }" clearable>
        </el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { editTag, getTag, getTagList } from '@/api/overview'
export default defineComponent({
  name: 'OverviewEditTag',
  components: {
    ElMessage
  }
})
interface AssignProps {
  visible: boolean
  ticketId: number
  projectName: string
}
interface Form {
  ticket_id?: number
  label_id: number[]
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false,
  ticketId: 0,
  projectName: ''
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
// business
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({
  label_id: [] 
})

const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value) return
    loading.value = true
    try {
      await editTag(form)
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}

// edit
const tagOpts = ref([])
onMounted(() => {
  form.ticket_id = props.ticketId
  // 获取标签树
  getTagList({
    project_name: props.projectName,
    lib_type: 1
  }).then((res: any) => {
    tagOpts.value = res.data
  })
  // 获取该工单的标签
  getTag({
    ticket_id: props.ticketId
  }).then((res: any) => {
    form.label_id = res.label_id
  })
})
</script>