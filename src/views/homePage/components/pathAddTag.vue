<template>
  <el-dialog v-model="value" width="600px"
    :title="$t('btn_batch_mark')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_add_tag')">
        <el-cascader v-model="form.label" style="width:100%" :options="tagOpts" filterable
          :disabled="false" :placeholder="$t('text_tag_placeholder')" :reserve-keyword="false"
          :props="{ multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }" clearable>
        </el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { getTagList, batchTagSave } from '@/api/overview'
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage
  }
})
interface EditTempLibProps {
  visible: boolean
  ticketIds: string[]
  paramsData?: Record<string, unknown>
}
interface Form {
  project: string
  label: number[]
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  paramsData: () => ({}),
  ticketIds: ()=>[],
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({} as Form)
const tagOpts = ref<any[]>([])
const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      await batchTagSave({ticket_ids: props.ticketIds, label_id: form.label})
      ElMessage.success($t('text_batch_tag_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}
// 获取游戏下的标签库
const getProjectTag = async() => {
  getTagList({
    project_name: form.project ? form.project : '',
    lib_type : 1
  }).then((res: any) => {
    if (res && res.data) {
      tagOpts.value = res.data
    }
  })
}

onMounted(() => {
  if (Object.keys(props.paramsData || {}).length > 0) {
    form.project = props.paramsData.project as string
    getProjectTag()
  }
})
</script>

<style scoped></style>