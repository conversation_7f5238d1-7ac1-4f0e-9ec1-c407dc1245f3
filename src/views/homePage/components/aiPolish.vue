<template>
  <el-dialog v-model="value" width="600px" :title="$t('btn_ai_polish')" :destroy-on-close="true" :before-close="close"
    :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" :rules="rules" ref="formRef">
      <el-form-item :label="$t('text_polish_require')" prop="polishRequire">
        <el-input type="textarea" :rows="5" maxlength="200" show-word-limit :placeholder="$t('know_m_rich_placeholder')" v-model="form.polishRequire" />
      </el-form-item>
    </el-form>
    <div class="btn-polish">
      <el-button type="primary" @click="submit">{{ $t('btn_polish_start') }}</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
const { t: $t } = useI18n()
interface AssignProps {
  visible: boolean
}
interface Form {
  polishRequire: string
}
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'customize', customizeContent: string): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}

const form = reactive<Form>({
  polishRequire: ''
})
const rules = reactive<FormRules>({
  polishRequire: [{ required: true, message: $t('know_m_rich_placeholder'), trigger: 'blur' }]
})
const formRef = ref<FormInstance>()
const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid) return
    emit('customize', form.polishRequire)
    close()
  })
}
</script>

<style lang="scss" scoped>
.btn-polish {
  display: flex;
  justify-content: center;
}
</style>