<template>
  <splitpanes class="split-box default-theme" vertical :push-other-panes="false">
    <!-- 左侧内容区域 -->
    <pane size="15">
      <splitpanes class="split-box o-left default-theme" horizontal :push-other-panes="false">
        <!-- 数据概览 -->
        <pane style="height:320px">
          <div class="title">
            <span class="iconfont icon-shujugailan"></span>
            <span class="text">{{ $t('text_data_overview') }}</span>
          </div>
          <el-scrollbar>
            <el-button v-for="(v, k) in ovButtonList" @click="shortcutHandle(v)"
              :class="[v.id === activeShortcutId ? 'activeBtn' : '']" :key="k">
              {{ $t(v.lable) }}: {{ overviewData[v.prop] }}
            </el-button>
          </el-scrollbar>
        </pane>
        <!-- 多维数据概览 -->
        <!-- <pane>
          <div class="title">
            <span class="iconfont icon-duoweiduziyoupouxi"> </span>
            <span class="text">{{ $t('text_mult_data_overview') }}</span>
          </div>
          <el-scrollbar>
            <el-button v-for="(v, k) in muButtonList" @click="shortcutHandle(v)"
              :class="[v.id === activeShortcutId ? 'activeBtn' : '']" :key="k">
              {{ $t(v.lable) }}: {{ overviewData[v.prop] }}
            </el-button>
          </el-scrollbar>
        </pane> -->
        <!-- 自定义数据概览 -->
        <pane >
          <div class="title" style="margin-bottom: 10px;">
            <span class="iconfont icon-duoweiduziyoupouxi"> </span>
            <span class="text">{{ $t('text_custom_data_overview') }}</span>
          </div>
          <el-scrollbar v-loading="tabsLoading">
            <el-menu v-if="openIndex.length > 0"  class="project-sortable-tree"  :default-openeds="openIndex">
              <template v-for="(v, k) in tabList"  :key="k">
                <el-sub-menu :index="v.project">
                  <template #title>
                    <DragIcon />
                    <span>{{ v.project }}</span>
                  </template>
                  <template v-for="(l, m) in v.tabs" :key="m">
                    <el-menu-item :data-id="l.id" :index="l.tab_name">
                      <el-button @click="handleClickTab(l)" :class="[l.id === activeTabId ? 'activeBtn' : '']" style="position: relative;">
                        <DragIcon />
                        {{ l.tab_name }}:{{ l.count }}
                        <el-link :disabled="l.operator !== userInfo.username" icon="Edit" :underline="false" class="edit-btn" @click.stop="handleEditTab(l)"></el-link>
                        <el-link :disabled="l.operator !== userInfo.username" icon="Delete" :underline="false" class="delete-btn" @click.stop="handleDelTab(l)"></el-link>
                      </el-button>
                    </el-menu-item>
                  </template>
                </el-sub-menu>
              </template>
            </el-menu>
          </el-scrollbar>
        </pane>
      </splitpanes>
    </pane>
    <!-- 中间内容区域 -->
    <pane>
      <splitpanes class="split-box default-theme" horizontal :push-other-panes="false">
        <pane size="25">
          <el-scrollbar class="pane-wapper">
            <el-form size="small" ref="searchFormRef" :inline="true" :model="searchForm" class="search-form">
              <!-- 游戏 -->
              <el-form-item prop="project">
                <el-select v-model="searchForm.project" @change="changeGameHandle" :placeholder="$t('text_game') "
                  multiple collapse-tags collapse-tags-tooltip clearable :reserve-keyword="false">
                  <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
                    :value="v.game_project"></el-option>
                </el-select>
              </el-form-item>
              <!-- 工单状态 -->
              <el-form-item prop="status">
                <el-select v-model="searchForm.status" :placeholder="$t('text_ticket_status')" multiple collapse-tags
                  collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.ConversionNode" :key="index" :label="v.name"
                    :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 工单来源 -->
              <el-form-item prop="scene">
                <el-select v-model="searchForm.scene" :placeholder="$t('text_ticket_by')" multiple collapse-tags
                  collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.SceneType" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 渠道包 -->
              <el-form-item prop="channel">
                <el-select v-model="searchForm.channel" :disabled="searchForm.project.length !== 1"
                  :placeholder="$t('text_channel')" :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip clearable filterable>
                  <template #header>
                    <el-checkbox
                      v-model="checkChannel"
                      :indeterminate="indeterminateChannel"
                      @change="channelCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="item in channelOpts" :key="item" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
              <!-- 渠道号（fpx游戏特有） -->
              <el-form-item prop="packageId">
                <el-select v-model="searchForm.packageId" :disabled="searchForm.project.length !== 1 || searchForm.channel.length === 0"
                  :placeholder="$t('text_channel_id')" :reserve-keyword="false" filterable clearable multiple collapse-tags>
                  <template #header>
                    <el-checkbox
                      v-model="checkPackageId"
                      :indeterminate="indeterminatePackageId"
                      @change="packageIdCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="item in packageIdOpts" :key="item" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
              <!-- 游戏版本 -->
              <el-form-item prop="game_version">
                <el-select v-model="searchForm.game_version" :disabled="searchForm.project.length !== 1"
                  :placeholder="$t('text_game_version')" :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip clearable filterable>
                  <template #header>
                    <el-checkbox
                      v-model="checkGameVersion"
                      :indeterminate="indeterminateGameVersion"
                      @change="gameVersionCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="item in gameVersionOpts" :key="item" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
              <!-- 标签条件 -->
              <el-form-item prop="tag_type">
                <el-select v-model="searchForm.tag_type" :placeholder="$t('text_please_change_tag_condition')"
                clearable :disabled="searchForm.project.length !== 1" @change="handleTagType">
                  <el-option v-for="(v, index) in enumList.TagType" :key="index" :label="v.name"
                    :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 工单标签 -->
              <el-form-item prop="label">
                <el-cascader v-model="searchForm.label" style="width:100%" :options="tagOpts" filterable
                  :disabled="searchForm.project.length !== 1 || !searchForm.tag_type || searchForm.tag_type === 4 || searchForm.tag_type === 1"
                  :placeholder="$t('text_tag_placeholder')"
                  collapse-tags
                  :reserve-keyword="false"
                  collapse-tags-tooltip
                  :max-collapse-tags="1"
                  :props="{ multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }" clearable>
                </el-cascader>
              </el-form-item>
              <!-- NPS -->
              <el-form-item prop="nps">
                <el-select v-model="searchForm.nps" placeholder="NPS" multiple collapse-tags collapse-tags-tooltip
                  clearable>
                  <el-option v-for="(v, index) in enumList.Nps" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 工单评星 -->
              <el-form-item prop="csi">
                <el-select v-model="searchForm.csi" :placeholder="$t('text_rating_star')" multiple collapse-tags
                  collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.Csi" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 工单语言 -->
              <el-form-item prop="language">
                <el-select v-model="searchForm.language" :placeholder="$t('text_ticket_lang')" multiple collapse-tags
                  collapse-tags-tooltip clearable>
                  <template #header>
                    <el-checkbox
                      v-model="checkLang"
                      :indeterminate="indeterminateLang"
                      @change="langCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="(v, index) in langList" :key="index" :label="v.name" :value="v.code"></el-option>
                </el-select>
              </el-form-item>
              <!-- 区服 -->
              <el-form-item prop="sid">
                <el-input v-model.trim="searchForm.sid" :placeholder="$t('text_sid_place')" clearable />
              </el-form-item>
              <!-- 工单单号 -->
              <el-form-item prop="ticket_ids">
                <el-input v-model="searchForm.ticket_ids" :placeholder="$t('text_ticket_num')" clearable />
              </el-form-item>
              <!-- 重开次数 -->
              <el-form-item prop="reopen_num">
                <el-input v-model="searchForm.reopen_num" :placeholder="$t('text_reopen_num')" clearable @input="validateNonNegativeInteger('reopen_num')" />
              </el-form-item>
              <!-- 升级次数 -->
              <el-form-item prop="upgrade_num">
                <el-input v-model="searchForm.upgrade_num" :placeholder="$t('text_upgrade_num')" clearable @input="validateNonNegativeInteger('upgrade_num')" />
              </el-form-item>
              <!-- 处理人查询维度+处理人 -->
              <el-form-item>
                <opsSelect v-model="searchForm.acceptor_type" :placeholder="$t('text_acceptor_type')"
                  :style="'min-width: 200px;'">
                  <el-option :label="$t('text_null')" :value="1"></el-option>
                  <el-option :label="$t('text_cs')" :value="2"></el-option>
                </opsSelect>
              </el-form-item>
              <!-- 处理类型 -->
              <el-form-item>
                <el-select v-model="searchForm.solve_type" :placeholder="$t('text_accept_type')" multiple collapse-tags
                  collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.TicketSolveType" :key="index" :label="v.name"
                    :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-input
                  class="input-with-select"
                  v-if="searchForm.acceptor_type === 0 || searchForm.acceptor_type === 1"
                  v-model="searchForm.acceptor"
                  clearable
                  :disabled="searchForm.acceptor_type === 0"
                  :placeholder="$t('text_please_input_user')" />
                <el-select
                  v-else
                  v-model="searchForm.acceptors"
                  :reserve-keyword="false"
                  :placeholder="$t('text_please_select_user')"
                  filterable clearable multiple collapse-tags>
                  <template #header>
                    <el-checkbox
                      v-model="checkAcceptors"
                      :indeterminate="indeterminateAcceptors"
                      @change="acceptorsCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="(v, index) in csList" :key="index" :label="v.account" :value="v.account"></el-option>
                </el-select>
              </el-form-item>
              <!-- 问题类型 -->
              <el-form-item prop="creator_type">
                <el-cascader v-model="searchForm.cat_id" :options="category" collapse-tags
                  :placeholder="$t('text_qtype')" :props="{ multiple: true, emitPath: false, value: 'id' }" clearable
                  :disabled="searchForm.project.length !== 1">
                  <template #default="{ data }">
                    <span :class="{ 'active': data.isInter == true }">{{ data.label }}</span>
                  </template>
                </el-cascader>
              </el-form-item>
              <!-- 相关信息 -->
              <el-form-item prop="field" style="width: 411px;">
                <el-input v-model="searchForm.field" :placeholder="$t('text_input_relevant_info') " clearable />
              </el-form-item>
              <!-- 备注消息 -->
              <el-form-item prop="remark" style="width: 411px;">
                <el-input v-model.trim="searchForm.remark" :placeholder="$t('text_remark_content')" clearable />
              </el-form-item>
              <!-- 系统标签 -->
              <el-form-item prop="system_label">
                <el-select v-model="searchForm.system_label" :placeholder="$t('text_system_label')" multiple
                  collapse-tags collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.TicketSystemTag" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 用户类型 -->
              <el-form-item prop="search_type">
                <opsSelect v-model="searchForm.search_type" :placeholder="$t('text_user_type')" clearable @change="searchForm.user_type = []">
                  <el-option v-for="(v, index) in enumList.SearchType" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </opsSelect>
              </el-form-item>
              <!-- 付费类型 -->
              <el-form-item prop="user_type">
                <opsSelect :disabled="!searchForm.search_type" v-model="searchForm.user_type" :placeholder="$t('text_pay_type')" multiple
                  collapse-tags collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.UserType" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </opsSelect>
              </el-form-item>
              <!-- 团队 -->
              <el-form-item prop="team_ids">
                <el-select v-model="searchForm.team_ids" :placeholder="$t('text_team')" :reserve-keyword="false" clearable filterable multiple collapse-tags>
                  <template #header>
                    <el-checkbox
                      v-model="checkTeam"
                      :indeterminate="indeterminate"
                      @change="teamCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="v in teamList" :key="v.team_id" :label="v.team_name" :value="v.team_id"></el-option>
                </el-select>
              </el-form-item>
              <!-- 提交人查询维度+提交人 -->
              <el-form-item>
                <el-input v-model="searchForm.creator" style="min-width: 410px;" clearable class="input-with-select"
                  :placeholder="$t('place_input')">
                  <template #prepend>
                    <opsSelect v-model="searchForm.creator_type" :placeholder="$t('text_creator_type')"
                      :style="'min-width: 200px;'" :clearable="false">
                      <el-option label="uid" :value="1"></el-option>
                      <el-option label="fpid" :value="2"></el-option>
                      <el-option :label="$t('text_player_nickname')" :value="3"></el-option>
                    </opsSelect>
                  </template>
                </el-input>
              </el-form-item>
              <!-- 累计付费金额 -->
              <el-form-item prop="pay_all">
                <el-input :placeholder="$t('text_total_pay')" v-model.number="searchForm.pay_all[0]" type="number" clearable
                  style="width: 50px;" /><span class="hor-line">-</span>
                <el-input :placeholder="$t('text_total_pay')" v-model.number="searchForm.pay_all[1]" type="number" clearable
                  style="width: 50px;" />
              </el-form-item>
              <!-- 创建时间 -->
              <el-form-item prop="created_at">
                <el-date-picker v-model="searchForm.created_at" format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm" type="datetimerange" :range-separator="$t('to')"
                  :start-placeholder="$t('text_create_time')" :end-placeholder="$t('text_create_time')">
                </el-date-picker>
              </el-form-item>
              <!-- 结案时间 -->
              <el-form-item prop="closed_at">
                <el-date-picker v-model="searchForm.closed_at" format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm" type="datetimerange" :range-separator="$t('to')"
                  :start-placeholder="$t('text_end_time')" :end-placeholder="$t('text_end_time')">
                </el-date-picker>
              </el-form-item>
              <!-- 评价时间 -->
              <el-form-item prop="evaluate_at">
                <el-date-picker v-model="searchForm.evaluate_at" format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm" type="datetimerange" :range-separator="$t('to')"
                  :start-placeholder="$t('text_evaluation_time')" :end-placeholder="$t('text_evaluation_time') ">
                </el-date-picker>
              </el-form-item>
              <!-- 是否VIP -->
              <el-form-item prop="is_vip">
                <el-checkbox v-model="searchForm.is_vip" label="VIP" border />
              </el-form-item>
              <!-- 是否升级 -->
              <el-form-item prop="is_upgrade">
                <el-checkbox v-model="searchForm.is_upgrade" :label="$t('text_is_upgrade') " border />
              </el-form-item>
              <!-- 是否SVIP -->
              <el-form-item prop="svip">
                <el-select v-model="searchForm.svip" :placeholder="$t('text_is_svip')" clearable>
                  <el-option label="是SVIP" value="1"></el-option>
                  <el-option label="非SVIP" value="2"></el-option>
                </el-select>
              </el-form-item>
              <!-- 是否VIP-New -->
              <el-form-item prop="vip_crm">
                <el-select v-model="searchForm.vip_crm" :placeholder="$t('text_is_vipnew')" clearable>
                  <el-option label="是VIP-New" value="1"></el-option>
                  <el-option label="非VIP-New" value="2"></el-option>
                </el-select>
              </el-form-item>
              <!-- 私域R级 -->
              <el-form-item prop="zone_vip_level">
                <el-select v-model="searchForm.zone_vip_level" :placeholder="$t('text_zone_vip_level')" multiple collapse-tags
                  collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.ZoneVipLevel?.sort((a, b) => a.name.localeCompare(b.name))" :key="index" :label="v.name" :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 搜索操作按钮 -->
              <el-form-item>
                <el-button type="primary" plain icon="Search" @click="search">{{ $t('btn_search') }}</el-button>
              </el-form-item>
              <!-- 重置按钮 -->
              <el-form-item>
                <el-button icon="Refresh" @click="reset">{{ $t('btn_reset') }}</el-button>
              </el-form-item>
              <!-- 下载按钮 -->
              <el-form-item>
                <el-button type="primary" icon="Download" @click="donwLoad" v-has="'overview:donwload'"
                  :loading="progState" :disabled="progState">{{ $t('btn_download') }}
                  <span v-if="progState">{{ progressNum + '%' }}</span>
                </el-button>
              </el-form-item>
              <!-- 批量指派按钮 -->
              <el-form-item>
                <el-button :disabled="checkTicketList.length === 0" v-has="'overview:batchassign'" @click="assignHandle({}, true)" size="small" plain
                  type="primary">{{ $t('btn_batch_assign')
                  }}</el-button>
              </el-form-item>
              <!-- 批量打标签 -->
              <el-form-item>
                <el-button type="primary" icon="PriceTag" @click="handlePatchTag" :disabled="checkTicketList.length === 0">{{ $t('btn_batch_mark') }}</el-button>
              </el-form-item>
              <!-- 固定tab -->
              <el-form-item>
                <el-button type="primary" icon="AddLocation" :disabled="!isObjectNonEmpty(searchForm)" @click="handleAddTabData">{{ $t('text_fixed_tab') }}</el-button>
              </el-form-item>
              <!-- 批量回复 -->
              <el-form-item>
                <el-button type="primary" icon="ChatLineRound" @click="handlePatchReply(1)" v-has="'overview:batchReply'"
                :disabled="checkTicketList.length === 0">{{ $t('text_batch_reply') }}</el-button>
              </el-form-item>
              <!-- 批量回复&关单 -->
              <el-form-item>
                <el-button type="primary" icon="ChatDotSquare" @click="handlePatchReply(2)" v-has="'overview:customsTicket'"
                :disabled="checkTicketList.length === 0">{{ $t('text_batch_reply_customs_declaration') }}</el-button>
              </el-form-item>
              <!-- 批量拒单 -->
              <el-form-item>
                <el-button type="primary" icon="CloseBold" @click="handlePatchRefuse" v-has="'overview:rejectTicket'"
                :disabled="checkTicketList.length === 0">{{ $t('text_batch_reject_the_order') }}</el-button>
              </el-form-item>
              <!-- 批量删除标签 -->
              <el-form-item>
                <el-button type="primary" @click="batchDelTagHandle" v-has="'overview:batchDeleteTag'" icon="Delete"
                :disabled="checkTicketList.length === 0">{{ $t('text_batch_del_tag') }}</el-button>
              </el-form-item>
              <!-- 批量备注 -->
              <el-form-item>
                <el-button type="primary" @click="batchRemarksHandle" v-has="'overview:batchRemarks'" icon="Edit"
                :disabled="checkTicketList.length === 0">{{ $t('text_batch_remarks') }}</el-button>
              </el-form-item>
            </el-form>
          </el-scrollbar>
        </pane>
        <pane>
          <el-card class="abbreviation-wapper" shadow="never" v-loading="poolLoading">
            <template #header>
              <slot name="header">
                <span class="dis-checkbox">
                  <el-checkbox v-model="checkAll" size="small" @change="onAllSelectChange"></el-checkbox>
                </span>
                <span class="list-total-tip">{{$t('text_total_tip')}}：{{ ticketListData.total }}</span>
                <!-- 排序 -->
                <el-form size="small" :inline="true" style="float: right;">
                  <el-form-item style="margin-right: 5px">
                    <div class="sort-box">
                      <el-link icon="CaretTop" :style="{ color: orderBy === 'asc' ? '#4aa181' : '#606266' }" :underline="false" @click="handleSortAsc"></el-link>
                      <el-link icon="CaretBottom" :style="{ color: orderBy === 'desc' ? '#4aa181' : '#606266' }" :underline="false" @click="handleSortDesc"></el-link>
                    </div>
                  </el-form-item>
                  <el-form-item>
                    <opsSelect v-model="poolSortType" :placeholder="$t('place_select')" :style="'width: 100px;'">
                      <el-option v-for="(v, index) in enumList.PoolSort" :key="index" :label="v.name"
                        :value="v.value"></el-option>
                    </opsSelect>
                  </el-form-item>
                </el-form>
              </slot>
            </template>
            <div class="dis-flex">
              <el-scrollbar class="ticket-list">
                <div v-for="(v, k) in ticketListData.list" v-if="ticketListData.list.length > 0" :key="k" class="item-waper">
                  <el-descriptions @click.stop="activeTicketHandle(v)" :column="3" size="small" border :id="'item' + v.ticket_id">
                    <template #title>
                      <div class="ticket-id">
                        <span class="dis-checkbox" @click.stop>
                          <el-checkbox style="float: left;" v-model="v.checked" @change="handleCheckItemChange(v)" />
                        </span>
                        <span style="display: inline-block;height: 32px;line-height: 32px;" :style="{ color: v.crm_vip_user_flag ? '#49A3A5' : '' }">
                          {{ $t('text_ticket') }}ID：{{ v.ticket_id }}
                        </span>
                        <span v-if="v.system_label && v.system_label.includes(13)" style="color: red;font-size: 12px;margin-left: 10px;">
                          {{ $t('text_right_to_use') }}
                        </span>
                      </div>
                    </template>
                    <template #extra>
                      <el-button @click.stop="handleCopy(v)" v-has="'overview:assign'" size="small" plain
                        type="primary">{{ $t('text_copy')
                        }}</el-button>
                      <el-button @click.stop="returnTicketPool(v)" v-has="'overview:returnpool'" size="small" plain
                        type="primary" :disabled="isDisabled(v.status)">{{ $t('text_return_work_order_pool')
                        }}</el-button>
                      <el-button @click.stop="assignHandle(v)" v-has="'overview:assign'" size="small" plain
                        type="primary">{{ $t('text_assign')
                        }}</el-button>
                    </template>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_game') }} </div>
                      </template>
                      {{ v.project ? v.project : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_acc_amount') }} </div>
                      </template>
                      {{ v.recharge }}$
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_current_handlers') }} </div>
                      </template>
                      {{ v.acceptor || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_ticket_status') }} </div>
                      </template>
                      <span :style="{ color: colorsMap[v.status], 'font-weight': 'bold' }">
                        {{ enumList.ConversionNode.find((item: Record<string, string | number>) => item.value ===
                          v.status)?.name || '-' }}
                      </span>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_pending_time') }} </div>
                      </template>
                      {{ v.waiting_time }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_service_rating') }} </div>
                      </template>
                      {{ v.csi === 0 ? '-' : v.csi }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label" :span="2">
                      <template #label>
                        <div> {{ $t('text_content') }} </div>
                      </template>
                      <span v-html="v.detail ? v.detail : $t('text_none')"></span>
                    </el-descriptions-item>
                  </el-descriptions>
                  <div class="coupled-data" v-if="v.dc_flag">
                    玩家 ( FPID: <span class="link-btn" @click="jumpHandle(v.account_id)">{{ v.account_id }}</span> ) 于 {{ v.dc_create_time }}, 已通过Discord来访
                  </div>
                </div>
                <el-empty v-else :description="$t('info_no_data')" />
              </el-scrollbar>
            </div>
            <template #footer>
              <el-pagination class="pagination" v-model:current-page="ticketListData.page" :small="true"
                :page-sizes="[20, 50, 100, 150, 200]" v-model:page-size="ticketListData.pageSize"
                :total="ticketListData.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </template>
          </el-card>
        </pane>
      </splitpanes>
    </pane>
    <!-- 右侧内容区域 -->
    <pane size="38.8" min-size="22" v-loading="activeInfoLoading">
      <el-scrollbar style="height:100%;">
        <div class="operating-space" v-if="Object.keys(activeTicketData).length > 0">
          <!-- 顶部信息 -->
          <div class="base-info">
            <el-descriptions direction="horizontal" size="small"
              :title="`${$t('text_ticket')}ID：${activeTicketData.top_info.ticket_id}`" :class="{ 'active-title': activeTicketData.crm_vip_user_flag }" :column="3">
              <template #extra>
                <span class="title">
                  {{ $t('text_processed_by') }}：{{ activeTicketData.top_info.acceptor ?
                  activeTicketData.top_info.acceptor : $t('text_none') }}
                </span>
              </template>
              <el-descriptions-item label-class-name="right-label">
                <template #label>
                  <span> {{ $t('text_game') }}： </span>
                </template>
                {{ activeTicketData.top_info.project ? activeTicketData.top_info.project : '-' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <span> {{ $t('text_player_nickname') }}： </span>
                </template>
                {{ activeTicketData.top_info.nickname ? activeTicketData.top_info.nickname : '-' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <span> FPID： </span>
                </template>
                {{ activeTicketData.top_info.account_id ? activeTicketData.top_info.account_id : '-' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <span> UID： </span>
                </template>
                {{ activeTicketData.top_info.uid ? activeTicketData.top_info.uid : '-' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <span> {{ $t('text_game_version') }}： </span>
                </template>
                {{ activeTicketData.top_info.app_version ? activeTicketData.top_info.app_version : '-' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <span> {{ $t('text_channel') }}： </span>
                </template>
                {{ activeTicketData.user_info.channel ? activeTicketData.user_info.channel : '-' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <span> {{ $t('text_channel_id') }}： </span>
                </template>
                {{ activeTicketData.user_info.packageId ? activeTicketData.user_info.packageId : '-' }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  <span> {{ $t('text_server') }}： </span>
                </template>
                {{ activeTicketData.top_info.sid ? activeTicketData.top_info.sid : '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="tab-info">
            <el-tabs v-model="activeTab" @tab-change="tabChangeHandle">
              <!-- 对话 -->
              <el-tab-pane :label="$t('text_conversation')" :name="1">
                <el-scrollbar style="height:100%;">
                  <dialogItem v-for="(v, k) in activeTicketData.commu_info" :key="k" :item-data="v" />
                </el-scrollbar>
              </el-tab-pane>
              <!-- 基础信息 -->
              <el-tab-pane :label="$t('text_base_info')" :name="2">
                <el-scrollbar style="height:100%;">
                  <el-descriptions size="small" border :column="1" style="cursor: auto; margin-bottom: 0px;">
                    <el-descriptions-item v-for="(item, index) in tabInfoBaseCloumns" :key="index" :label="item.label"
                      label-class-name="base-info-label">
                      <template v-if="item.prop === 'tag_name'">
                        <span>{{ activeTicketData.user_info[item.prop].length > 0 ? '' : $t('text_none') }}</span>
                        <span v-for="(val, key) in activeTicketData.user_info[item.prop]" :key="key">
                          {{ val }}
                          {{ key === activeTicketData.user_info[item.prop].length - 1 ? '' : '、' }}
                        </span>
                      </template>
                      <template v-else-if="['ip', 'packageId'].includes(item.prop)">
                        <span>{{ activeTicketData.user_info[item.prop] || '-'  }}</span>
                      </template>
                      <template v-else>
                        {{ activeTicketData.user_info[item.prop] }}
                      </template>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-scrollbar>
              </el-tab-pane>
              <!-- 工单历史 -->
              <el-tab-pane :label="$t('text_history_ticket')" :name="3">
                <el-scrollbar style="height:100%; padding:0px 15px">
                  <el-descriptions v-for="(v, k) in activeTicketData.record_info.data"
                    v-if="activeTicketData.record_info.data.length > 0" @click.stop="activeTicketHandle(v, true)"
                    :key="k" :title="`${$t('text_ticket')}ID：${v.ticket_id}`" :column="2" size="small" border>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_game') }} </div>
                      </template>
                      {{ v.project ? v.project : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_acc_amount') }} </div>
                      </template>
                      {{ v.recharge }}$
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_ticket_status') }} </div>
                      </template>
                      {{ enumList.ConversionNode.find((item: Record<string, string|number>) => item.value ===
                        v.status)?.name || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_pending_time') }} </div>
                      </template>
                      {{ v.waiting_time }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label" :span="2">
                      <template #label>
                        <div> {{ $t('text_content') }} </div>
                      </template>
                      {{ v.detail ? v.detail : $t('text_none')}}
                    </el-descriptions-item>
                  </el-descriptions>
                  <el-empty v-else :description="$t('info_no_data')" />
                </el-scrollbar>
              </el-tab-pane>
              <!-- 分单日志 -->
              <el-tab-pane :label="$t('text_suborder_log')" :name="4">
                <el-scrollbar style="height:100%; padding: 0px 15px;">
                  <el-timeline class="time-line">
                    <el-timeline-item v-for="(item, index) in activeTicketData.history" :key="index"
                      :timestamp="item.created_at" placement="top">
                      <el-card>
                        <h4>{{ item.type }}</h4>
                        <p>{{ item.remark }}</p>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </el-scrollbar>
              </el-tab-pane>
              <!-- 金币查询 -->
              <el-tab-pane :label="$t('text_gold_query')" :name="5">
                <div class="query-flex">
                  <el-form size="small" ref="coinQueryFormRef" :inline="true" class="query-box">
                    <el-form-item>
                      <el-date-picker v-model="coinQueryForm.created_at" format="YYYY-MM-DD HH:mm" style="width: 260px" :disabled-date="disabledDate"
                        value-format="YYYY-MM-DD HH:mm" type="datetimerange" range-separator="~" @calendar-Change="calendarChange" clearable
                        :start-placeholder="$t('text_start_time')" :end-placeholder="$t('text_over_time')" popper-class="no-clearbtn" />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" plain icon="Search" @click="queryHandle('coin')">{{ $t('btn_search') }}</el-button>
                    </el-form-item>
                  </el-form>
                  <el-table :data="coinListData" size="small" class="query-table" v-loading="coinQueryLoading">
                    <el-table-column prop="reason" label="原因"/>
                    <el-table-column prop="change" label="变化数" width="100px"/>
                    <el-table-column prop="before" label="变化前" width="100px"/>
                    <el-table-column prop="after" label="变化后" width="100px"/>
                    <el-table-column prop="change_time" label="时间(UTC+0)" />
                  </el-table>
                </div>
              </el-tab-pane>
              <!-- 支付查询 -->
              <el-tab-pane :label="$t('text_pay_query')" :name="6">
                <div class="query-flex">
                  <el-form size="small" ref="payQueryFormRef" :inline="true" class="query-box">
                    <el-form-item>
                      <el-date-picker v-model="payQueryForm.created_at" format="YYYY-MM-DD HH:mm" style="width: 260px" :disabled-date="disabledDate"
                        value-format="YYYY-MM-DD HH:mm" type="datetimerange" range-separator="~" @calendar-Change="calendarChange" clearable
                        :start-placeholder="$t('text_start_time')" :end-placeholder="$t('text_over_time')" popper-class="no-clearbtn" />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" plain icon="Search" @click="queryHandle('pay')">{{ $t('btn_search') }}</el-button>
                    </el-form-item>
                  </el-form>
                  <el-table :data="payListData" size="small" class="query-table" v-loading="payQueryLoading">
                    <el-table-column prop="product_name" label="商品名称"/>
                    <el-table-column prop="pay_channel" label="支付渠道"/>
                    <el-table-column prop="price" label="价格"/>
                    <el-table-column prop="basic_price" label="基础价格"/>
                    <el-table-column prop="currency" label="币种" />
                    <el-table-column prop="status" label="支付结果" />
                    <el-table-column prop="paid_at" label="时间(UTC+0)" />
                  </el-table>
                </div>
              </el-tab-pane>
              <!-- 商品查询 -->
              <el-tab-pane :label="$t('text_goods_query')" :name="7">
                <div class="query-flex">
                  <el-form size="small" ref="goodsQueryFormRef" :inline="true" class="query-box">
                    <el-form-item>
                      <el-date-picker v-model="goodsQueryForm.created_at" format="YYYY-MM-DD HH:mm" style="width: 260px" :disabled-date="disabledDate"
                        value-format="YYYY-MM-DD HH:mm" type="datetimerange" range-separator="~" @calendar-Change="calendarChange" clearable
                        :start-placeholder="$t('text_start_time')" :end-placeholder="$t('text_over_time')" popper-class="no-clearbtn" />
                    </el-form-item>
                    <el-form-item>
                      <el-select-v2
                        v-model="goodsQueryForm.item_id"
                        filterable
                        clearable
                        :reserve-keyword="false"
                        :options="gameGoodsList"
                        :placeholder="$t('text_chose_goods')"
                        style="width: 220px"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" plain icon="Search" @click="queryHandle('goods')">{{ $t('btn_search') }}</el-button>
                    </el-form-item>
                  </el-form>
                  <el-table :data="goodsListData" size="small" class="query-table" v-loading="goodsQueryLoading">
                    <el-table-column prop="reason" label="原因"/>
                    <el-table-column prop="item_name" label="物品名称"/>
                    <el-table-column prop="change" label="变化数" width="100px"/>
                    <el-table-column prop="before" label="变化前" width="100px"/>
                    <el-table-column prop="after" label="变化后" width="100px"/>
                    <el-table-column prop="change_time" label="时间(UTC+0)" />
                  </el-table>
                </div>
              </el-tab-pane>
              <!-- 登陆查询 -->
              <el-tab-pane :label="$t('text_login_query')" :name="8">
                <div class="query-flex">
                  <el-form size="small" ref="loginQueryFormRef" :inline="true" class="query-box">
                    <el-form-item>
                      <el-date-picker v-model="loginQueryForm.created_at" format="YYYY-MM-DD HH:mm" style="width: 260px" :disabled-date="disabledDate"
                        value-format="YYYY-MM-DD HH:mm" type="datetimerange" range-separator="~" @calendar-Change="calendarChange" clearable
                        :start-placeholder="$t('text_start_time')" :end-placeholder="$t('text_over_time')" popper-class="no-clearbtn" />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" plain icon="Search" @click="queryHandle('login')">{{ $t('btn_search') }}</el-button>
                    </el-form-item>
                  </el-form>
                  <el-table :data="loginListData" size="small" class="query-table" v-loading="loginQueryLoading">
                    <el-table-column prop="fp_device_id" label="登陆设备"/>
                    <el-table-column prop="device_type" label="设备型号"/>
                    <el-table-column prop="ip" label="登陆IP"/>
                    <el-table-column prop="ip_loc" label="IP位置"/>
                    <el-table-column prop="login_at" label="时间(UTC+0)" />
                  </el-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <!-- 工单回复处理区域 -->
          <div class="process-area" v-loading="polishLoading" :element-loading-text="$t('text_ai_operation')+'...'">
            <div style="height:100%;">
              <!-- 评分和评价信息展示 -->
              <div class="score-evalute" v-if="activeTicketData.ticket_appraise !== null" style="float: left;">
                <el-descriptions size="small" :column="1" direction="horizontal" style="padding:10px">
                  <el-descriptions-item v-for="(item, index) in tabInfoEvaluateCloumns" :key="index"
                    :label="item.label">
                    {{ activeTicketData.ticket_appraise[item.prop] }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <div style="display: flex;flex-wrap: wrap;">
                <el-button size="small" class="remark-btn" type="primary" plain icon="Document" @click="aiSummarizeHandle">
                  {{ $t('btn_ai_summarize') }}
                </el-button>
                <div style="width: auto;">
                  <el-button size="small" class="remark-btn" type="primary" plain icon="DocumentChecked" @click="aiPolishHandle" :disabled="!polishValue || !csContent">
                    {{ $t('btn_ai_polish') }}
                  </el-button>
                  <el-select size="small" class="remark-btn" v-model="polishValue" clearable placeholder="Select" style="width: 80px">
                    <el-option v-for="item in enumList.AIPolishLabel" :key="item.value" :label="item.name" :value="item.value"/>
                  </el-select>
                </div>
                <el-button size="small" class="remark-btn" type="primary" plain icon="DocumentAdd" @click="aiPreRecovery">
                  {{ $t('btn_ai_prerecovery') }}
                </el-button>
                <el-button size="small" class="remark-btn" type="primary" plain icon="Memo" @click="editTempHandle">
                  {{ $t('text_quote_template') }}
                </el-button>
                <el-button size="small" class="remark-btn" type="primary" plain icon="PriceTag" @click="editTagHandle">
                  {{ $t('text_edit_tag') }}
                </el-button>
                <el-button size="small" class="remark-btn" type="primary" plain icon="EditPen" @click="remarkHandle">
                  {{ $t('btn_add_remark') }}
                </el-button>
                <el-button v-if="activeTicketData.system_label && activeTicketData.system_label.includes(13)" size="small" class="remark-btn" type="text" style="color: red;">
                  {{ $t('text_caution_handle') }}
                </el-button>
              </div>
              <div class="eidt-box" v-if="!inoperable">
                <opsEditer v-model="csContent" v-model:uploading="isUploading"></opsEditer>
              </div>
              <div class="btn-box" v-if="!inoperable">
                <!-- 升级/降级 -->
                <el-button v-has="'overview:upgrade'" size="small"
                  :type="activeTicketData.top_info.priority === 1 ? 'primary' : 'danger'" @click="updateHandle">{{
                  activeTicketData.top_info.priority === 1 ? $t('btn_upgrade') : $t('btn_downgrade') }}</el-button>
                <!-- 流转 -->
                <el-button v-has="'overview:exchange'" size="small" type="primary" @click="exchangeHandle"
                  :disabled="noPermission || inoperable">{{ $t('btn_transfer') }}</el-button>
                <!-- 回复 -->
                <el-button v-has="'overview:reply'" size="small" type="primary" @click="replyHandle(1)"
                  :disabled="noPermission || inoperable || csContent === '' || isUploading">{{ $t('btn_reply')
                  }}</el-button>
                <!-- 回复关单 -->
                <el-button v-has="'overview:replyclose'" size="small" type="primary" @click="replyHandle(2)"
                  :disabled="noPermission || inoperable || csContent === '' || isUploading">{{ $t('btn_reply_close')
                  }}</el-button>
                <!-- 拒单 -->
                <el-button v-has="'overview:replyrefuse'" size="small" type="primary" @click="replyHandle(3)"
                  :disabled="noPermission || inoperable || isUploading">{{ $t('btn_refuse') }}</el-button>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-else :description="$t('info_no_data')" />
      </el-scrollbar>
    </pane>
  </splitpanes>
  <overviewAssign v-if="assignVisible" v-model:visible="assignVisible" :ticket-id="assignTicketId"
    @success="searchSuccess" />
  <overviewBatchAssign v-if="batchAssignVisible" v-model:visible="batchAssignVisible" :ticket-ids="checkTicketList"
    @success="searchSuccess" />
  <overviewTransfer v-if="transferVisible" v-model:visible="transferVisible"
    :ticket-id="activeTicketData.top_info.ticket_id" @success="searchSuccess" />
  <overviewRemark v-if="remarkVisible" v-model:visible="remarkVisible" :ticket-id="activeTicketData.top_info.ticket_id"
    @success="searchSuccess" />
  <overviewEditTag v-if="editTagVisible" v-model:visible="editTagVisible"
    :ticket-id="activeTicketData.top_info.ticket_id" :project-name="activeTicketData.top_info.project"
    @success="searchSuccess" />
  <overviewQuoteTemp v-if="editTempVisible" v-model:visible="editTempVisible"
    :project-name="activeTicketData.top_info.project" @success="handleContent" />
  <aiSummarize v-if="aiSummarizeVisible" v-model:visible="aiSummarizeVisible"
    :ticket-id="activeTicketData.top_info.ticket_id" />
  <aiPolish v-if="aiPolishVisible" v-model:visible="aiPolishVisible" @customize="aiPolishHandle" />
  <replyAndClose v-if="replyAndCloseVisible" v-model:visible="replyAndCloseVisible"
    :replyContent="csContent" :ticket-id="activeTicketData.top_info.ticket_id" @sync-data="replyHandle" />
  <patch-tag v-if="patchTagVisible" v-model:visible="patchTagVisible" :ticket-ids="checkTicketList" :params-data="projectData" @success="searchSuccess" />
  <add-tab v-if="tabVisible" v-model:visible="tabVisible" :params-data="tabData" @success="getTabCount" />
  <batch-reply v-if="batchReplyVisible" v-model:visible="batchReplyVisible" :ticket-ids="checkTicketList" :op-type="operateType" @success="searchSuccess"/>
  <!-- 批量删除标签组件 -->
  <batch-deltag v-if="batchDelTagVisible" v-model:visible="batchDelTagVisible" :batch-ids="checkTicketList" :params-data="projectData" @success="searchSuccess"/>
  <!-- 批量备注组件 -->
  <batch-remark v-if="batchRemarkVisible" v-model:visible="batchRemarkVisible" :ticket-ids="checkTicketList" @success="searchSuccess"/>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import Sortable from 'sortablejs'
import { overview, getChannelList, questionOptsAdmin, getTagList, ticketPoolList, ticketPoolDetail,
  replyTicket, ticketChangeLevel, ticketDownload, aiPolishApi, aiPreReply, returnPool, ticketBatchReply,
  getTicketTabLists, deleteTicketTab, getTicketCount, getCoinList, getPayList, getGoodsList, getLoginList, getGoodsOpts, getGameVersions, updateTabSettingOrder } from '@/api/overview'
import { getAcceptorList, getTeamList } from '@/api/assignConfig'
import { useEnumStore, useUserInfoStore, useAppStore } from '@/stores'
import type { FormInstance, CheckboxValueType } from 'element-plus'
import overviewAssign from './components/assign.vue'
import batchDeltag from './components/batchDelTicketTag.vue'
import overviewBatchAssign from './components/batchAssign.vue'
import overviewTransfer from './components/transfer.vue'
import overviewRemark from './components/remark.vue'
import overviewEditTag from './components/editTag.vue'
import overviewQuoteTemp from './components/quoteTemplate.vue'
import dialogItem from './components/dialogItem.vue'
import aiSummarize from './components/aiSummarize.vue'
import aiPolish from './components/aiPolish.vue'
import replyAndClose from './components/replyAndClose.vue'
import patchTag from './components/pathAddTag.vue'
import addTab from './components/addTab.vue'
import batchReply from './components/batchReply.vue'
import batchRemark from './components/batchRemark.vue'
import DragIcon from '@/components/DragIcon.vue'
import { saveUrlParamsToStorage, getParamsFromStorage, clearStoredParams } from '@/utils/urlParams'
export default defineComponent({
  name: 'Overview',
  components: {
    Splitpanes, Pane, overviewAssign, overviewBatchAssign, ElMessage, ElMessageBox,
    overviewTransfer, overviewRemark, overviewEditTag, overviewQuoteTemp,
    aiSummarize, aiPolish, replyAndClose, patchTag, addTab, batchReply, batchDeltag, batchRemark
  }
})
interface shortcutButtonT {
  id: number
  lable: string
  shortcut: Record<string, unknown>
}
interface SearchForm {
  project: string[]
  created_at: Array<string>
  closed_at: Array<string>
  evaluate_at: Array<string>
  status: number[]
  tag_type: number | null
  scene: number[]
  channel: string[]
  packageId: string[]
  game_version: string[]
  label: number[]
  nps: number[]
  csi: number[]
  language: string[]
  sid: string
  ticket_ids: string
  acceptor: string
  acceptors: string[]
  acceptor_type: number
  creator: string
  creator_type: number
  cat_id: string[]
  remark: string
  field: string
  system_label: number[]
  is_upgrade: boolean
  is_vip: boolean
  svip: string
  vip_crm: string
  zone_vip_level: string[] // 新增私域R级筛选字段
  reopen_num: string
  upgrade_num: string
  checked?: boolean
  page?: number
  page_size?: number
  search_type?: number
  user_type?: number[]
  team_ids?: number[]
  tab_name?: string
  id?: number
  public?: number,
  pay_all?: [number, number] | []
}
type SearchFormKey = keyof SearchForm
type SearchFormValue = SearchForm[keyof SearchForm]
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>)
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum)
const progState = ref<boolean>(false)
watch(progressNum, (n) => {
  progState.value = (n < 100 && n > -1) ? true : false
})
const colorsMap = {
  1: '#F56C6C', // 待接单-红色
  2: '#E6A23C', // 待处理-黄色
  4: '#E6A23C', // 处理中玩家已回复-黄色
  6: '#E6A23C' // 重开-黄色
}
const searchFormInitData = {
  project: [],
  created_at: [],
  closed_at: [],
  evaluate_at: [],
  status: [],
  scene: [],
  channel: [],
  packageId: [],
  game_version: [],
  label: [],
  nps: [],
  csi: [],
  language: [],
  sid: '',
  ticket_ids: '',
  acceptor: '',
  acceptors: [],
  acceptor_type: 0,
  tag_type: null,
  remark: '',
  field: '',
  system_label: [],
  cat_id: [],
  creator: '',
  creator_type: 0,
  is_upgrade: false,
  is_vip: false,
  svip: '',
  vip_crm: '',
  zone_vip_level: [], // 新增私域R级初始值
  search_type: 0,
  user_type: [],
  team_ids: [],
  reopen_num: '',
  upgrade_num: '',
  solve_type: [],
  pay_all: [undefined, undefined]
}
const ovButtonList = [
  {
    id: 1,
    lable: 'text_ticket_total',
    shortcut: searchFormInitData,
    prop: 'Count'
  },
  {
    id: 2,
    lable: 'text_pending_ticket',
    shortcut: {
      status: [1, 2, 4, 6]
    },
    prop: 'PendingCount'
  },
  {
    id: 3,
    lable: 'text_auto_reply_work_orders',
    shortcut: {
      acceptor_type: 2,
      acceptor: 'SystemRole'
    },
    prop: 'AutoReplyCount'
  },
  {
    id: 4,
    lable: 'text_pending_ticket_my',
    shortcut: {
      status: [2, 3, 4, 6],
      acceptor_type: 2,
      acceptor: 'CurrentUser'
    },
    prop: 'UserPendingCount'
  },
  {
    id: 5,
    lable: 'text_finish_ticket_my',
    shortcut: {
      status: [5, 7, 8],
      acceptor_type: 2,
      acceptor: 'CurrentUser'
    },
    prop: 'UserCompletedCount'
  }
]
const muButtonList = [
  {
    id: 5,
    lable: 'text_pending_ticket_zh',
    shortcut: {
      language:['zh-cn', 'zh-tw'],
      status: [1, 2, 4]
    },
    prop: 'PendingCnCount'
  },
  {
    id: 6,
    lable: 'text_pending_ticket_vip',
    shortcut: {
      status: [1, 2, 4, 6],
      is_vip: true
    },
    prop: 'PendingVipCount'
  },
  {
    id: 7,
    lable: 'text_ticket_upgraded',
    shortcut: {
      status: [1, 2, 3, 4, 6],
      is_upgrade: true
    },
    prop: 'PriorityCount'
  }
]
const isUploading = ref(false)
const orderBy = ref('')
const patchTagVisible = ref(false)
const checkTicketList = ref<number[]>([])
const batchAssignVisible = ref(false)
const checkProject = ref<string[]>([])
const checkAll = ref(false)
const projectData = ref({})
const tabsLoading = ref(false)
const tabVisible = ref(false)
const tabData = ref<SearchForm>({} as SearchForm)
const tabList = ref<any[]>([])
const openIndex = ref<string[]>([])
const batchReplyVisible = ref(false)
const operateType = ref<number | null>()
const ticketStatus = ref<number[]>([])
const csList = ref<Record<string, string>[]>([])
const teamList = ref<Record<string, number>[]>([])
const overviewData: Record<string, number> = reactive({
  Count: 0,
  PendingCount: 0,
  UserPendingCount: 0,
  UserCompletedCount: 0,
  PendingCnCount: 0,
  PendingVipCount: 0,
  PriorityCount: 0,
  AutoReplyCount: 0,
})
const getCsList = async () => {
  const res = await getAcceptorList({})
  csList.value = res
  // 获取团队列表  true：不分页的全部数据
  const teamRes = await getTeamList({isAll:true})
  teamList.value = teamRes.data
}
getCsList()
// 获取概览数据
const getOverview = async() => {
  try {
    const res = await overview({})
    Object.keys(overviewData).forEach((key) => {
      overviewData[key] = res[key]
    })
  } catch (error) {
    console.log(error)
  }
}
getOverview()

// 获取固定tab列表
const getTabCount =  async() => {
  tabsLoading.value = true
  tabList.value = []
  try {
    const tabLists = await getTicketTabLists({})
    const tabCounts = await getTicketCount({})
    if (tabLists.data.length === 0) {
      tabsLoading.value = false
      return
    }
    tabLists.data.forEach((item1: any) => {
      const projectName = item1.project
      const newTabList = [] as any
      openIndex.value.push(projectName) // 默认展开所有tab菜单项
      item1.tab.forEach((tab1: any) => {
        // 在tabCounts中寻找对应的项目
        const matchingProject = tabCounts.detail.find((item2: any) => item2.project === projectName)
        if (matchingProject) {
          // 找到匹配的项目后，查找对应的tab_name
          const matchingTab = matchingProject.tab.find((tab2: any) => tab2.tab_name === tab1.tab_name)
          if (matchingTab) {
            // 如果匹配成功，输出对应的值
            newTabList.push({
              ...tab1,
              count: matchingTab.count,
            })
          }
        }
      })
      // 组成新的数据结构
      tabList.value.push({
        project: projectName,
        tabs: newTabList,
      })
      tabsLoading.value = false
    })
  } catch (error) {
    console.log(error)
    tabsLoading.value = false
  }
}
getTabCount()
// 查询模块
const gameList = computed(() => useEnumStore().gameList)
const langList = computed(() => useEnumStore().LangsList)
const enumList = computed(() => useEnumStore().enumList)
const searchFormRef = ref<FormInstance>()
const poolSortType = ref(0)
// 切换游戏，拉取渠道包,问题类型
const category = ref([])
const channelOpts = ref<string[]>([]) //渠道包
const channelIDs = ref<string[]>([]) // 渠道号（fpx游戏特有）
const packageIdOpts = ref<string[]>([]) // 渠道号选项
const checkPackageId = ref<boolean>(false)
const indeterminatePackageId = ref<boolean>(false)
const gameVersionOpts = ref<string[]>([]) // 游戏版本选项
const checkGameVersion = ref<boolean>(false)
const indeterminateGameVersion = ref<boolean>(false)
const tagOpts = ref<any[]>([])
const changeGameHandle = (val: string[]) => {
  searchForm.value.channel = []
  searchForm.value.packageId = []
  searchForm.value.game_version = []
  searchForm.value.cat_id = []
  searchForm.value.label = []
  channelOpts.value = []
  packageIdOpts.value = []
  gameVersionOpts.value = []
  tagOpts.value = []
  category.value = []
  if (val.length === 1 && val[0]) {
    getChannelList({ project_name: val[0] ? val[0] : '' }).then((res: Record<string, string[]>) => {
      channelOpts.value = Object.keys(res)
      // 存储渠道包对应的渠道号映射
      channelIDs.value = Object.values(res).flat()
    })
    getGameVersions({ project: val[0] ? val[0] : '' }).then((res: {
      app_version: string,
      project: string
    }[]) => {
      res.forEach((item) => {
        gameVersionOpts.value.push(item.app_version)
      })
    })
    questionOptsAdmin({ project: val[0] ? val[0] : '', scope: 0 }).then((res: any) => {
      category.value = res
    })
    getTagList({ project_name: val[0] ? val[0] : '', lib_type: 1 }).then((res: any) => {
      if (res && res.data) {
        tagOpts.value = res.data
      }
    })
  }
}
const ticketListData = reactive({
  list: [] as Record<string, unknown>[], // 表格数据
  total: 0, // 总数
  page: 1, // 当前页
  pageSize: 20, // 每页条数
})
const poolLoading = ref(false)
const search = async () => {
  poolLoading.value = true

  // pay_all 参数处理，如果都为空测不传
  let payAll: number[] = [
        !searchForm.value.pay_all?.[0] ? 0 : Number(searchForm.value.pay_all[0]),
        !searchForm.value.pay_all?.[1] ? 0 : Number(searchForm.value.pay_all[1])
      ]
  if (payAll[0] === 0 && payAll[1] === 0) {
    payAll = []
  }

  const params = Object.assign({}, searchForm.value, {
    order: orderBy.value,
    page: ticketListData.page,
    page_size: ticketListData.pageSize,
    sort_by: poolSortType.value,
    pay_all:payAll
  })
  ticketListData.list = []
  await ticketPoolList(params).then((res: any) => {
    ticketListData.list = res.data
    ticketListData.total = res.total
    ticketListData.page = res.current_page
    ticketListData.pageSize = res.per_page
  }).finally(() => {
    poolLoading.value = false
    refreshTable()
    handleGetCheckedState()
  })
}
const donwLoad = () => {
  const params = Object.assign({}, searchForm.value)


  // pay_all 参数处理，如果都为空测不传
  let payAll: number[] = [
        !searchForm.value.pay_all?.[0] ? 0 : Number(searchForm.value.pay_all[0]),
        !searchForm.value.pay_all?.[1] ? 0 : Number(searchForm.value.pay_all[1])
      ]
  if (payAll[0] === 0 && payAll[1] === 0) {
    payAll = []
  }
  params.pay_all = payAll as [number, number] | []

  ticketDownload(params)
}
const handleSizeChange = (val: number) => {
  ticketListData.page = 1
  ticketListData.pageSize = val
  search()
}
const handleCurrentChange = (val: number) => {
  ticketListData.page = val
  search()
}
// 复制当前url，拼接工单中的ticket_id和游戏project
const handleCopy = (data: Record<string, unknown>) => {
  const url = window.location.origin + window.location.pathname
  const ticketId = data.ticket_id
  const project = data.project
  const newUrl = `${url}?ticket_ids=${ticketId}&project=${project}`
  navigator.clipboard.writeText(newUrl)
  ElMessage.success($t('text_copy_success'))
}
// 指派
const assignVisible = ref(false)
const assignTicketId = ref(0)
// 批量指派
const assignHandle = (data: Record<string, unknown>, isbatch: boolean) => {
  if (isbatch) {
    batchAssignVisible.value = true
  } else {
    assignVisible.value = true
    assignTicketId.value = data.ticket_id as number
  }
}
// 回工单池
const returnTicketPool = (data: Record<string, unknown>) => {
  returnPool({ticket_id: data.ticket_id}).then(() => {
    ElMessage.success($t('text_success'))
    searchSuccess()
  })
}
// 处理回工单池按钮禁用逻辑
const isDisabled = (status: number) => {
  // 定义需要禁用按钮的状态集合
  const disabledStatuses = [1, 3, 5, 7, 8]
  return disabledStatuses.includes(status)
}
// 快捷查询功能
const searchForm = ref<SearchForm>({ ...searchFormInitData })
const activeShortcutId = ref(0)
const activeTabId = ref<number>(0)
const shortcutHandle = (v: shortcutButtonT) => {
  activeTabId.value = 0
  checkTicketList.value = []
  activeShortcutId.value = v.id
  searchForm.value = { ...searchFormInitData }
  Object.keys(v.shortcut).forEach((key) => {
    if (key in searchForm.value) {
      if (key === 'acceptor' && v.shortcut[key] === 'CurrentUser') {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = userInfo.value.username as SearchFormValue
      } else {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = v.shortcut[key] as SearchFormValue
      }
    }
  })
  search()
}
// 自动加载第一个按钮查询数据
shortcutHandle(ovButtonList[0])

// 重置
const reset = () => {
  searchFormRef.value?.resetFields()
  shortcutHandle(ovButtonList[0])
  orderBy.value = ''
}

const router = useRouter()
const route = useRoute()
// 操作区域
const activeInfoLoading = ref(false)
const activeTicketData = ref<{ top_info?: any }>({})
const anchoredItem = ref<number | null>(null)
const gameGoodsList = ref([]) // 游戏商品列表下拉
const activeTicketHandle = (v: any, isHistory?: boolean) => {
  if (isHistory) {
    const newTab = router.resolve({
      name: 'Overview',
      query: { ticket_id: v.ticket_id }
    })
    window.open(newTab.href, '_blank')
    return
  }
  // 清空查询条件和精分数据
  coinQueryForm.created_at = []
  payQueryForm.created_at = []
  goodsQueryForm.created_at = []
  loginQueryForm.created_at = []
  coinListData.value = []
  payListData.value = []
  goodsListData.value = []
  loginListData.value = []

  activeInfoLoading.value = true
  activeTicketData.value = {}
  anchoredItem.value = v.ticket_id
  ticketPoolDetail({ ticket_id: v.ticket_id }).then((res: any) => {
    activeTicketData.value = { ...res, crm_vip_user_flag: v.crm_vip_user_flag as boolean, system_label: v.system_label as number[] }
    getGoodsOpts({ project: activeTicketData.value.top_info.project }).then((res: any) => {
      gameGoodsList.value = res.data
    })
    if (isHistory) {
      activeTab.value = 1
    }
  }).finally(() => {
    activeInfoLoading.value = false
  })
}
const activeTab = ref(1)
const tabChangeHandle = (v: number) => {
  queryPickDate.value = null
}
const tabInfoBaseCloumns = [
  { prop: 'tag_name', label: $t('text_ticket_tag') },
  { prop: 'category', label: $t('text_qtype') },
  { prop: 'created_at', label: $t('text_create_time') },
  { prop: 'channel', label: $t('text_channel') },
  { prop: 'packageId', label: $t('text_channel_id') },
  { prop: 'device_type', label: $t('text_device_type') },
  { prop: 'rom_gb', label: $t('text_rom_gb') },
  { prop: 'remain_rom', label: $t('text_remain_rom') },
  { prop: 'recharge', label: $t('text_recharge_amount') },
  { prop: 'lang', label: $t('text_player_language') },
  { prop: 'country', label: $t('text_country') },
  { prop: 'os_version', label: $t('text_os_version') },
  { prop: 'ip', label: 'IP' },
  { prop: 'zone_vip_level', label: $t('text_zone_vip_level') }
]
const tabInfoEvaluateCloumns = [
  { prop: 'created_at', label: $t('text_evaluation_time')+':' },
  { prop: 'csi', label: $t('text_service_feel')+':' },
  { prop: 'recommendation_level', label: $t('text_nps')+':' },
  { prop: 'remark', label: $t('text_other_explain')+':' },
]

// 备注
const remarkVisible = ref(false)
const remarkHandle = () => {
  remarkVisible.value = true
}
// 编辑标签
const editTagVisible = ref(false)
const editTagHandle = () => {
  editTagVisible.value = true
}
// 引用模板
const editTempVisible = ref(false)
const editTempHandle = () => {
  editTempVisible.value = true
}
// AI总结
const aiSummarizeVisible = ref<boolean>(false)
const aiSummarizeHandle = () => {
  aiSummarizeVisible.value = true
}
// AI润色
const polishLoading = ref<boolean>(false)
const aiPolishVisible = ref<boolean>(false)
const polishValue = ref<number>(1)
const aiPolishHandle = (txt: string) => {
  if (polishValue.value === 5 && !txt.length) {
    aiPolishVisible.value = true
    return
  }
  polishLoading.value = true
  aiPolishApi({
    ticket_id: activeTicketData.value.top_info.ticket_id,
    content: csContent.value,
    style: txt.length ? txt : (enumList.value.AIPolishLabel as { value: number, name: string }[]).find((item: Record<string, string|number>) => item.value === polishValue.value)?.name
  }).then((res: any) => {
    csContent.value = res
  }).catch((err: any) => {
    ElMessage.error(err)
  }).finally(() => {
    polishLoading.value = false
  })
}
// AI预回复
const aiPreRecovery = () => {
  polishLoading.value = true
  aiPreReply({
    ticket_id: activeTicketData.value.top_info.ticket_id
  }).then((res: any) => {
    csContent.value = res
  }).catch((err: any) => {
    ElMessage.error(err)
  }).finally(() => polishLoading.value = false)
}
// 回复 + 回复关单 + 拒单
const csContent = ref('')
const replyAndCloseVisible = ref<boolean>(false)
const replyHandle = (op_type: number, formInfo?: any) => {
  if (!csContent.value && op_type !== 3) {
    ElMessage.warning($t('text_cannot_empty'))
    return
  }
  if (op_type === 2 && !formInfo) {
    replyAndCloseVisible.value = true
    return
  }
  replyTicket({
    ticket_id: activeTicketData.value.top_info.ticket_id,
    content: csContent.value,
    op_type: op_type,
    is_sync_ai_elfin: formInfo ? formInfo.is_sync_ai_elfin : false,
    question: formInfo ? formInfo.question : '',
    answer: formInfo ? formInfo.answer : ''
  }).then(() => {
    ElMessage.success($t('text_success'))
    csContent.value = ''
    searchSuccess()
  })
}
const handleContent = (val: string) => {
  csContent.value = val
}
// 升级 + 降级
const noPermission = computed(() => {
  return activeTicketData.value.top_info.acceptor !== userInfo.value.username
})
const inoperable = computed(() => {
  return  [5, 7, 8].includes(activeTicketData.value.top_info.status)
})
const updateHandle = () => {
  ticketChangeLevel({
    ticket_id: activeTicketData.value.top_info.ticket_id,
    upgrade: activeTicketData.value.top_info.priority === 1 ? 2 : 1
  }).then((res: any) => {
    ElMessage.success($t('text_success'))
    searchSuccess()
  })
}
// 流转
const transferVisible = ref(false)
const exchangeHandle = () => {
  transferVisible.value = true
}

// 操作成功后刷新列表和概览数据和工单详情
const searchSuccess = () => {
  handleBeforeUnload()
  checkTicketList.value = []
  search()
  getOverview()

  // 如果展示了工单详情，则需要刷新工单详情
  if (activeTicketData.value.top_info) {
    activeTicketHandle({ ticket_id: activeTicketData.value.top_info.ticket_id })
  }
}
// 刷新后锚点到上次点击的数据
// 监听checkTicketList变化，如果有值，锚点到最后勾选的那个数据
watch(checkTicketList, (n, o) => {
  if (n.length > 0 && n.length > o.length) {
    anchoredItem.value = n[n.length - 1]
  }
})

const refreshTable = () => {
  if (anchoredItem.value) {
    const element = document.querySelector(`#item${anchoredItem.value}`)
    if (element) {
      element.scrollIntoView()
    }
  }
}
// 排序
const handleSortAsc = () => {
  orderBy.value = 'asc'
}
const handleSortDesc = () => {
  orderBy.value = 'desc'
}
const uniqueArr = ref<number[]>([])

// 团队全选
const checkTeam = ref<boolean>(false)
const indeterminate = ref<boolean>(false)
watch(() => searchForm.value.team_ids, (val) => {
  if (val?.length === 0) {
    checkTeam.value = false
    indeterminate.value = false
  } else if (val?.length === teamList.value.length) {
    checkTeam.value = true
    indeterminate.value = false
  } else {
    indeterminate.value = true
  }
})
const teamCheckAll = (val: CheckboxValueType) => {
  indeterminate.value = false
  if (val) {
    searchForm.value.team_ids = teamList.value.map((_) => _.team_id)
  } else {
    searchForm.value.team_ids = []
  }
}

// 渠道包全选
const checkChannel = ref<boolean>(false)
const indeterminateChannel = ref<boolean>(false)
watch(() => searchForm.value.channel, (val) => {
  if (val?.length === 0) {
    checkChannel.value = false
    indeterminateChannel.value = false
  } else if (val?.length === channelOpts.value.length) {
    checkChannel.value = true
    indeterminateChannel.value = false
  } else {
    indeterminateChannel.value = true
  }
})
const channelCheckAll = (val: CheckboxValueType) => {
  indeterminateChannel.value = false
  if (val) {
    searchForm.value.channel = channelOpts.value
  } else {
    searchForm.value.channel = []
  }
}

// 语言全选
const checkLang = ref<boolean>(false)
const indeterminateLang = ref<boolean>(false)
watch(() => searchForm.value.language, (val) => {
  if (val?.length === 0) {
    checkLang.value = false
    indeterminateLang.value = false
  } else if (val?.length === langList.value.length) {
    checkLang.value = true
    indeterminateLang.value = false
  } else {
    indeterminateLang.value = true
  }
})
const langCheckAll = (val: CheckboxValueType) => {
  indeterminateLang.value = false
  if (val) {
    searchForm.value.language = langList.value.map((_) => _.code)
  } else {
    searchForm.value.language = []
  }
}

// 客服全选
const checkAcceptors = ref<boolean>(false)
const indeterminateAcceptors = ref<boolean>(false)
watch(() => searchForm.value.acceptors, (val) => {
  if (val?.length === 0) {
    checkAcceptors.value = false
    indeterminateAcceptors.value = false
  } else if (val?.length === csList.value.length) {
    checkAcceptors.value = true
    indeterminateAcceptors.value = false
  } else {
    indeterminateAcceptors.value = true
  }
})
const acceptorsCheckAll = (val: CheckboxValueType) => {
  indeterminateAcceptors.value = false
  if (val) {
    searchForm.value.acceptors = csList.value.map((_) => _.account)
  } else {
    searchForm.value.acceptors = []
  }
}

// 渠道号全选功能
watch(() => searchForm.value.packageId, (val) => {
  if (val?.length === 0) {
    checkPackageId.value = false
    indeterminatePackageId.value = false
  } else if (val?.length === packageIdOpts.value.length) {
    checkPackageId.value = true
    indeterminatePackageId.value = false
  } else {
    indeterminatePackageId.value = true
  }
})
const packageIdCheckAll = (val: CheckboxValueType) => {
  indeterminatePackageId.value = false
  if (val) {
    searchForm.value.packageId = packageIdOpts.value
  } else {
    searchForm.value.packageId = []
  }
}

// 游戏版本全选功能
watch(() => searchForm.value.game_version, (val) => {
  if (val?.length === 0) {
    checkGameVersion.value = false
    indeterminateGameVersion.value = false
  } else if (val?.length === gameVersionOpts.value.length) {
    checkGameVersion.value = true
    indeterminateGameVersion.value = false
  } else {
    indeterminateGameVersion.value = true
  }
})
const gameVersionCheckAll = (val: CheckboxValueType) => {
  indeterminateGameVersion.value = false
  if (val) {
    searchForm.value.game_version = gameVersionOpts.value
  } else {
    searchForm.value.game_version = []
  }
}

// 监听渠道包选择变化
watch(() => searchForm.value.channel, (val) => {
  // 重置渠道号选择
  searchForm.value.packageId = []
  packageIdOpts.value = []

  if (val && val.length > 0 && searchForm.value.project.length === 1) {
    // 获取选中渠道包对应的渠道号
    getChannelList({ project_name: searchForm.value.project[0] }).then((res: Record<string, string[]>) => {
      const selectedChannelIds = val.reduce((acc: string[], channel: string) => {
        if (res[channel]) {
          acc.push(...res[channel])
        }
        return acc
      }, [])
      // 去重
      packageIdOpts.value = [...new Set(selectedChannelIds)]
    })
  }
}, { deep: true })

// 全选
const onAllSelectChange = () => {
  const clearSelection = () => {
    checkTicketList.value = []
    checkProject.value = []
    ticketStatus.value = []
  }

  if (checkAll.value) {
    clearSelection()
    localStorage.setItem(`checkedAll`, 'true')
    ticketListData.list.forEach((item: any) => {
      item.checked = true
      localStorage.setItem(`checkedState_${item.ticket_id}`, JSON.stringify(item.checked))
      checkTicketList.value.push(item.ticket_id)
      checkProject.value.push(item.project)
      ticketStatus.value.push(item.status)
    })
  } else {
    localStorage.removeItem(`checkedAll`)
    ticketListData.list.forEach((item: any) => {
      item.checked = false
      localStorage.removeItem(`checkedState_${item.ticket_id}`)
    })
    clearSelection()
  }
}
// 单选
const handleCheckItemChange = (val: { checked: boolean, ticket_id: number, project: string, status: number }) => {
  const updateList = (list: any[], value: any, action: 'add' | 'remove') => {
    const index = list.indexOf(value)
    if (action === 'add') {
      list.push(value)
    } else if (action === 'remove' && index > -1) {
      list.splice(index, 1)
    }
  }
  if (val.checked) {
    updateList(checkTicketList.value, val.ticket_id, 'add')
    updateList(checkProject.value, val.project, 'add')
    updateList(ticketStatus.value, val.status, 'add')
    localStorage.setItem(`checkedState_${val.ticket_id}`, JSON.stringify(val.checked))
  } else {
    updateList(checkTicketList.value, val.ticket_id, 'remove')
    updateList(checkProject.value, val.project, 'remove')
    updateList(ticketStatus.value, val.status, 'remove')
    localStorage.removeItem(`checkedState_${val.ticket_id}`)
  }
}
// 批量打标签
const handlePatchTag = () => {
  const projectList = [...new Set(checkProject.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_patch_tags_only_single_game'))
    return
  }
  patchTagVisible.value = true
  projectData.value = {
    project: projectList.join(',')
  }
}
// 批量回复
const handlePatchReply = (val: number) => {
  const projectList = [...new Set(checkProject.value)]
  const statusList = [...new Set(ticketStatus.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_batch_reply_error_message'))
    return
  }
  if(statusList.includes(5) || statusList.includes(7) || statusList.includes(8)) {
    ElMessage.error($t('text_not_allowed_to_reply_ticket'))
    return
  }
  batchReplyVisible.value = true
  operateType.value = val
}

// 批量删除标签
const batchDelTagVisible = ref(false)
const batchDelTagHandle = () => {
  const projectList = [...new Set(checkProject.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_batch_del_tags_only_single_game'))
    return
  }
  batchDelTagVisible.value = true
  projectData.value = {
    project: projectList.join(',')
  }
}

// 批量备注
const batchRemarkVisible = ref(false)
const batchRemarksHandle = () => {
  const projectList = [...new Set(checkProject.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_batch_mark_only_single_game'))
    return
  }
  batchRemarkVisible.value = true
}

// 批量拒单
const handlePatchRefuse = () => {
  const projectList = [...new Set(checkProject.value)]
  const statusList = [...new Set(ticketStatus.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_batch_reply_error_message'))
    return
  }
  if(statusList.includes(5) || statusList.includes(7) || statusList.includes(8)) {
    ElMessage.error($t('text_not_allowed_to_reply_ticket'))
    return
  }
  ElMessageBox.confirm(
    `${$t('text_select_sure_batch_refuse')}`,
    `${$t('text_batch_reject_the_order')}`,
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ticketBatchReply({ticket_ids: checkTicketList.value, op_type: 3})
    ElMessage.success($t('text_batch_operate_success'))
  })
}
// 固定tab
const handleAddTabData = () => {
  const { project } = searchForm.value
  if(project.length > 1) {
    ElMessage.error($t('text_only_supports_creating_single_game'))
    return
  }
  tabVisible.value = true
  const params = Object.assign({}, searchForm.value, {
    page: ticketListData.page,
    page_size: ticketListData.pageSize
  })
  tabData.value = params
}
// 固定tab-点击事件
const handleClickTab = (v: {id: number, detail: SearchForm }) => {
  searchFormRef.value?.resetFields()
  activeShortcutId.value = 0
  activeTabId.value = v.id
  if (v.detail) {
    const params = { ...v.detail }
    // 其他属性赋值
    if(Number(params.tag_type) === 0) {
      params.tag_type = null
    }
    params.page = ticketListData.page
    params.page_size = ticketListData.pageSize
    searchForm.value = params
  }
  search()
}
// 编辑tab
const handleEditTab = (v: { id: number, tab_name: string, public: number }) => {
  tabVisible.value = true
  tabData.value = {
    ...searchForm.value,
    tab_name: v.tab_name,
    id: v.id,
    public: v.public
  }
}
// 删除tab
const handleDelTab = async (v: { id: number, tab_name: string }) => {
  ElMessageBox.confirm(`确定要删除名称为: ( ${v.tab_name} )的Tab吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    tabsLoading.value = true
    deleteTicketTab({ id: v.id }).then(() => {
      ElMessage.success($t('text_del_success'))
      getTabCount()
      tabsLoading.value = false
      reset()
    }).catch((error: any) => {
      console.log(error)
      tabsLoading.value = false
    })
  })
}
const handleTagType = () => {
  searchForm.value.label = []
}
// 判断对象value是否有值
const isObjectNonEmpty = (obj: any): boolean => {
  if(obj.project.length === 0) {
    return false
  }
  // 检查对象是否为空或未定义
  if (!obj || typeof obj !== 'object') {
    return false
  }
  // 使用for...in循环遍历对象的所有可枚举属性
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      // 检查值是否不是undefined、null、空字符串""或空数组[]
      if (
        value !== undefined &&
        value !== null &&
        !(typeof value === 'string' && value === '') &&
        !(Array.isArray(value) && value.length === 0)
      ) {
        // 如果找到至少一个非空值，则返回true
        return true
      }
    }
  }
  // 如果没有找到任何非空值，则返回false
  return false
}
const handleBeforeUnload = () => {
  ticketListData.list.forEach(ticket => {
    const savedCheckedState = localStorage.getItem(`checkedState_${ticket.ticket_id}`)
    if (savedCheckedState !== null) {
      ticket.checked = false
      localStorage.removeItem(`checkedState_${ticket.ticket_id}`)
    }
  })
}
const handleGetCheckedState = () => {
  ticketListData.list.forEach(ticket => {
    const savedCheckedState = localStorage.getItem(`checkedState_${ticket.ticket_id}`)
    if (savedCheckedState !== null) {
      ticket.checked = JSON.parse(savedCheckedState)
    }
  })
}

// 查询模块
const queryPickDate = ref()
// 限制选择的时间范围前后2天
const limit = 2 * 24 * 60 * 60 * 1000
const calendarChange = (val: Date[]) => {
  const minDate = val[0]
  const maxDate = val[1]
  queryPickDate.value = minDate.getTime()
  if (maxDate) queryPickDate.value = null
}
const disabledDate = (time: any) => {
  if (queryPickDate.value) {
    return (
      time.getTime() < queryPickDate.value - limit ||
      time.getTime() > queryPickDate.value + limit
    )
  }
}

const coinQueryForm = reactive<{created_at: Array<string>}>({
  created_at: []
})
const payQueryForm = reactive<{created_at: Array<string>}>({
  created_at: []
})
const goodsQueryForm = reactive<{created_at: Array<string>, item_id?: string}>({
  created_at: []
})
const loginQueryForm = reactive<{created_at: Array<string>}>({
  created_at: []
})

const coinQueryFormRef = ref<FormInstance>()
const payQueryFormRef = ref<FormInstance>()
const goodsQueryFormRef = ref<FormInstance>()
const loginQueryFormRef = ref<FormInstance>()

const coinListData = ref<Record<string, unknown>[]>([])
const payListData = ref<Record<string, unknown>[]>([])
const goodsListData = ref<Record<string, unknown>[]>([])
const loginListData = ref<Record<string, unknown>[]>([])

const coinQueryLoading = ref(false)
const payQueryLoading = ref(false)
const goodsQueryLoading = ref(false)
const loginQueryLoading = ref(false)

const queryHandle = (type: string) => {
  if (type === 'coin') {
    coinQueryLoading.value = true
    coinListData.value = []
    getCoinList({
      ...coinQueryForm,
      project: activeTicketData.value.top_info.project,
      uid: activeTicketData.value.top_info.uid
    }).then((res: any) => {
      coinListData.value = res
    }).finally(() => {
      coinQueryLoading.value = false
    })
  }
  if (type === 'pay') {
    payQueryLoading.value = true
    getPayList({
      ...payQueryForm,
      project: activeTicketData.value.top_info.project,
      uid: activeTicketData.value.top_info.uid
    }).then((res: any) => {
      payListData.value = res
    }).finally(() => {
      payQueryLoading.value = false
    })
  }
  if (type === 'goods') {
    goodsQueryLoading.value = true
    getGoodsList({
      ...goodsQueryForm,
      project: activeTicketData.value.top_info.project,
      uid: activeTicketData.value.top_info.uid
    }).then((res: any) => {
      goodsListData.value = res
    }).finally(() => {
      goodsQueryLoading.value = false
    })
  }
  if (type === 'login') {
    loginQueryLoading.value = true
    getLoginList({
      ...loginQueryForm,
      project: activeTicketData.value.top_info.project,
      uid: activeTicketData.value.top_info.uid
    }).then((res: any) => {
      loginListData.value = res
    }).finally(() => {
      loginQueryLoading.value = false
    })
  }
}

// 耦合信息跳转路由
const jumpHandle = (v: string) => {
  const newTab = router.resolve({
    name: 'Discord',
    query: { fpid: v }
  })
  window.open(newTab.href, '_blank')
}

let timer: any = null
let tabTimer: any = null
onMounted(async () => {
  await useEnumStore().forceUpdateGameList()
  // 检查URL参数
  const { project, ticket_ids } = route.query

  // 是否存在无效的游戏项目
  const validProjects = []
  const invalidProjects = []

  // 如果URL中有参数，保存到sessionStorage
  if (project || ticket_ids) {
    saveUrlParamsToStorage(project, ticket_ids)
  }

  // 获取参数（优先从URL获取，没有则从sessionStorage获取）
  const projectParam = project || getParamsFromStorage().project
  const ticketIdsParam = ticket_ids || getParamsFromStorage().ticketIds

  if (projectParam) {
    const projectArray = typeof projectParam === 'string' ? projectParam.split(',') : projectParam
    console.log('projectArray', projectArray)


    // 获取gameList的值
    const gameListValue = Object.keys(gameList.value)

    // 验证每个project是否存在于gameList中
    projectArray.forEach(proj => {
      const exists = gameListValue.some(game => game === proj)
      if (exists) {
        validProjects.push(proj)
      } else {
        invalidProjects.push(proj)
      }
    })

    // 如果有无效的project，显示提示信息
    if (invalidProjects.length > 0) {
      const invalidNames = invalidProjects.map(proj => {
        const game = gameListValue.find(g => g.value === proj)
        return game ? game.app_name : proj
      }).join(', ')
      ElMessage.warning(`${$t('text_invalid_projects')}: ${invalidNames}`)
    }

    // 只赋值有效的project
    if (validProjects.length > 0) {
      searchForm.value.project = validProjects
    }
  }

  if (ticketIdsParam && validProjects.length > 0) {
    searchForm.value.ticket_ids = ticketIdsParam as string
  }

  // 如果有参数，触发查询
  if (searchForm.value.project?.length > 0 || searchForm.value.ticket_ids) {
    await search()
    // 查询完成后清除sessionStorage中的参数，避免再次加载
    clearStoredParams()
    if (ticketListData.list.length > 0) {
      activeTicketHandle(ticketListData.list[0])
    }
  }

  if (route.query.ticket_id) {
    if (route.query.origin === 'coupler') {
      // 来自耦合功能的跳转
      searchForm.value.ticket_ids = route.query.ticket_id as string
      await search()
      if (ticketListData.list.length > 0) {
        activeTicketHandle(ticketListData.list[0])
      } else {
        ElMessage.error('数据错误，并未找到对应的聊天')
      }
    } else {
      activeTicketHandle({ ticket_id: Number(route.query.ticket_id) })
    }
  }
  // 每1分钟刷新一次数据
  timer = setInterval(() => {
    search()
    getOverview()
  }, 30000)
  // 自定义数据每个5分钟刷新一次
  tabTimer = setInterval(() => {
    getTabCount()
  }, 300000)
  if(!localStorage.getItem(`checkedAll`)) {
    checkAll.value = false
  }
  // 监听浏览器刷新事件
  window.addEventListener('beforeunload', handleBeforeUnload)

  // 初始化拖拽
  nextTick(() => {
    initSortable()
  })

  // 监听tabList变化重新初始化拖拽
  watch(tabList, () => {
    nextTick(() => {
      initSortable()
    })
  }, { deep: true })
})
onUnmounted(() => {
  clearInterval(timer)
  clearInterval(tabTimer)
  // 移除监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

const validateNonNegativeInteger = (field: string) => {
  if (!/^\d*$/.test(searchForm.value[field as keyof SearchForm] as string)) {
    searchForm.value[field as keyof SearchForm] = (searchForm.value[field as keyof SearchForm] as string).replace(/\D/g, '') as never
  }
}

const updateSortable = () => {
  const menuEl = document.querySelectorAll('.project-sortable-tree .el-sub-menu')
  if (!menuEl) return

  const sort = [];
  menuEl.forEach((el) => {
    const project = el.querySelector(".el-sub-menu__title span").innerText
    const tabs = el.querySelectorAll('.el-menu-item')

    const sortTabs = [];
    tabs.forEach((tab) => {
      const tabId = tab.getAttribute("data-id");
      if (tabId) {
        sortTabs.push(Number(tabId));
      }
    })

    sort.push({
      project: project,
      tab: sortTabs
    })
  })

  updateTabSettingOrder({
    sort_setting: JSON.stringify(sort)
  }).then(() => {
    ElMessage.success("排序成功")
  }).catch(() => {
    ElMessage.error("排序失败")
  })
}

const initSortable = () => {
  const menuEl = document.querySelector('.project-sortable-tree')
  if (!menuEl) return

  new Sortable(menuEl, {
    animation: 150,
    handle: '.el-sub-menu__title',
    onEnd: ({ newIndex, oldIndex, to, from, item }) => {
      updateSortable()
    }
  })

  const menuSubEl = document.querySelectorAll('.project-sortable-tree .el-menu--inline')
  menuSubEl.forEach((el) => {
    new Sortable(el, {
      animation: 150,
      handle: '.el-button',
      onEnd: ({ newIndex, oldIndex, to, from, item }) => {
        updateSortable()
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.split-box {
  height: 100%;
  .pane-wapper {
    box-sizing: border-box;
    padding: 10px 5px;
  }
  .o-left {
    .el-scrollbar {
      height: calc(100% - 62px);
    }
    &:deep(.el-sub-menu__title) {
      height: 40px;
      line-height: 40px;
      &:hover {
        background-color: transparent;
      }
    }
    &:deep(.el-menu-item) {
      padding: 0;
      &:hover {
        background-color: transparent;
      }
    }
    .delete-btn {
      padding: 5px;
      position: absolute;
      right: -10px;
      visibility: hidden;
    }
    .edit-btn {
      position: absolute;
      right: 20px;
      visibility: hidden;
    }
    &:deep(.el-button) {
      &:hover {
        .delete-btn, .edit-btn {
          visibility: visible !important;
          color: #4aa181;
          background-color: #edf6f2;
          transition: background-color 0.2s
        }
      }
    }
    .activeBtn {
      color: var(--el-button-hover-text-color);
      border-color: var(--el-button-hover-border-color);
      background-color: var(--el-button-hover-bg-color);
      outline: none;
    }
    .el-button {
      display: block;
      margin: 0px auto 20px;
      width: 90%;
      overflow: hidden;
    }
    .title {
      font-size: 18px;
      height: 40px;
      line-height: 40px;
      font-weight: bold;
      text-align: center;
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      background-image: linear-gradient(to right, rgba(39, 177, 236, 0.8), rgba(74, 161, 129, 0.8));
      box-shadow: 0px 2px 7px -3px rgba(0, 0, 0, 0.6);
      margin-bottom: 20px;
      .text {
        margin-left: 10px;
      }
    }
  }
}
.abbreviation-wapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .dis-checkbox {
    float: left;
    margin-right: 5px;
  }
  .list-total-tip {
    float: left;
    margin-top: 5px;
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-regular);
  }
  &:deep(.my-label) {
    width: 120px;
    // text-align: right;
  }
  &:deep(.right-label) {
    // width: 120px;
    text-align: right;
  }
  &:deep(.el-form--inline .el-form-item) {
    margin-bottom: 10px;
    margin-right: 0px;
  }
  &:deep(.el-card__header, .el-card__footer) {
    padding: 10px 10px 0px;
  }

  &:deep(.el-card__footer) {
    border: 0px;
    padding: 10px;
  }

  &:deep(.el-card__body) {
    flex-grow: 1;
    padding: 10px 0px;
    overflow: hidden;
    .el-descriptions {
      cursor: pointer;
      width: 96%;
      margin: 0px auto;
      padding: 8px;
      border-radius: 5px 5px 0px 0px;
      background-color: #f8f9fa;
    }
    .coupled-data {
      background: var(--el-color-danger-light-9);
      padding: 4px 9px 2px;
      margin: 0px auto 0px;
      width: 96%;
      border-radius: 0px 0px 5px 5px;
      color: var(--el-text-color-regular);
      font-size: 12px;
      line-height: 1.5em;
      .link-btn {
        color: var(--el-color-primary);
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
        line-height: 1.5em;
      }
    }
    .item-waper {
      margin-bottom: 20px;
      border-radius: 5px;
      overflow: hidden;
    }
  }
  .dis-flex {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .ticket-list {
      width: 100%;
      flex-grow: 1;
    }
  }
  .pagination {
    float: right;
  }
  .sort-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 5px;
    &:deep(.el-icon) {
      margin-top: -5px;
      font-size: 16px;
    }
  }
}
.operating-space {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 10px 15px;
  .title {
    color: var(--el-text-color-primary);
    font-size: 14px;
    font-weight: bold;
  }
}
.tab-info {
  height: 428px;
  padding-bottom: 0px;
  border-bottom: 2px solid #e4e7ed;
  &:deep(.el-tabs) {
    height: 100%;
  }
  &:deep(.el-tabs__header) {
    margin-bottom: 10px;
  }
  &:deep(.el-tabs__content) {
    height: calc(100% - 55px);
    .el-tab-pane {
      height: 100%;
      .el-descriptions {
        cursor: pointer;
        width: 100%;
        margin: 0px auto 30px;
        padding: 8px;
        border-radius: 5px;
        background-color: #f8f9fa;
      }
    }
  }
  &:deep(.base-info-label) {
    min-width: 120px;
  }
  .score-evalute{
    :deep(.el-descriptions__body) {
      padding: 10px;
    }
  }
}
.time-line {
  padding: 0px;
  &:deep(.el-card__body) {
    padding: 0px 20px 6px;
  }
}

.process-area {
  .eidt-box {
    // height: 260px;
    margin-bottom: 10px;
  }
  .remark-btn {
    float: right;
    margin: 10px 0px 10px 10px;
  }
  .btn-box {
    .el-button {
      margin:5px 10px 10px;
    }
  }
}

.active-title {
  :deep .el-descriptions__title {
    color: #49A3A5;
  }
}
.query-flex {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .query-box {
    .el-form-item {
      margin-right: 3px !important;
    }
    .el-form-item--small {
      margin-bottom: 5px !important;
    }
  }
  .query-table {
    width: 100%;
    flex-grow: 1;
  }
}

.el-menu {
  border-right: none;
  .el-sub-menu {
    .el-menu-item {
      padding: 0;
      height: auto;
      line-height: normal;
    }
  }
}
</style>
