<template>
  <el-dialog v-model="value" width="600px"
    :title="$t('btn_batch_mark')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_add_tag')">
        <el-cascader v-model="form.tag" style="width:100%" :options="discordTagData" filterable
          :placeholder="$t('text_tag_placeholder')"
          collapse-tags
          :reserve-keyword="false"
          collapse-tags-tooltip
          :max-collapse-tags="1"
          :props="{ multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }" clearable>
        </el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { batchAddTag, getTagList } from '@/api/chatting'
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage
  }
})
interface EditTempLibProps {
  visible: boolean
  userIds: string[]
  paramsData?: Record<string, unknown>
}
interface Form {
  tag: string[],
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  paramsData: () => ({}),
  userIds: ()=>[],
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = reactive<Form>({} as Form)
const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      await batchAddTag({project: props.paramsData.project, tag: form.tag.join(','), dsc_user_id_list: [...new Set(props.userIds)]})
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}
// 获取discord标签库
const discordTagData = ref<[]>([])
const getDiscordTag = async() => {
  try {
    const res = await getTagList({ project_name: props.paramsData.project, lib_type: 2 })
    if (res && Array.isArray(res.data)) {
      discordTagData.value = res.data
    } else {
      console.error('Invalid response:', res)
    }
  } catch (error) {
    console.error('Error discord lists:', error)
  }  
}
getDiscordTag()

onMounted(() => {
  // if (Object.keys(props.paramsData || {}).length > 0) {
  //   form.project = props.paramsData.project as string
  // }
})
</script>

<style scoped></style>