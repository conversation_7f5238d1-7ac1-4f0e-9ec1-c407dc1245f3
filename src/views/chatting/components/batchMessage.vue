<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_batch_private_message')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form class="form" label-position="top" :model="form" ref="formRef">
      <!-- 选中的uid -->
      <el-form-item :label="$t('text_change_uids') + '（' + arrayLength + '）'">
        <el-input
          type="textarea"
          :rows="4"
          :placeholder="$t('text_change_uid_multiple_commas')"
          v-model.trim="form.inputValue"
          @input="handleInput"
          @keydown.enter.prevent
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('text_batch_message_content')">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleTabClick">
          <el-tab-pane :label="$t('send_text')" name="first">
            <el-form-item>
              <el-input
                type="textarea"
                :rows="8"
                :placeholder="$t('text_please_batch_message')"
                v-model="form.content"
                resize="none"
              >
              </el-input>
              <div class="message-box">
                <!-- 表情功能 -->
                <el-popover
                  :width="'auto'"
                  popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 0px; background: transparent;border-radius:8px;"
                >
                  <template #reference>
                    <div class="emoji-btn tool-btn">
                      <svg
                        t="1720582777160"
                        class="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="4323"
                        width="17"
                        height="17"
                      >
                        <path
                          d="M512 0C229.2 0 0 229.2 0 512s229.2 512 512 512 512-229.2 512-512S794.8 0 512 0z m0 960C265 960 64 759 64 512S265 64 512 64s448 201 448 448-201 448-448 448z"
                          p-id="4324"
                          fill="currentColor"
                        ></path>
                        <path
                          d="M320 405.3m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
                          p-id="4325"
                          fill="currentColor"
                        ></path>
                        <path
                          d="M704 405.3m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
                          p-id="4326"
                          fill="currentColor"
                        ></path>
                        <path
                          d="M512 810.7c117.8 0 213.3-95.5 213.3-213.3H298.7c0 117.8 95.5 213.3 213.3 213.3z"
                          p-id="4327"
                          fill="currentColor"
                        ></path>
                      </svg>
                    </div>
                  </template>
                  <template #default>
                    <EmojiPicker
                      @select="addEmoji"
                      :disable-sticky-group-names="true"
                      :static-texts="{
                        placeholder: $t('text_search_emoji'),
                        skinTone: $t('text_skin_tone'),
                      }"
                      :group-names="{
                        recent: $t('text_recent'),
                        smileys_people: $t('text_peple'),
                        animals_nature: $t('text_nature'),
                        food_drink: $t('text_food'),
                        activities: $t('text_activities'),
                        travel_places: $t('text_travel'),
                        objects: $t('text_objects'),
                        symbols: $t('text_symbols'),
                        flags: $t('text_flags'),
                      }"
                      :display-recent="true"
                    />
                  </template>
                </el-popover>
              </div>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane :label="$t('send_file')" name="second">
            <el-upload
              ref="upload"
              class="pic-upload"
              :action="`/gateway/proxy/cs_ticket/all/api/addons/upload`"
              :on-success="handleSuccess"
              :on-remove="handleRemove"
              :headers="{ 'admin-gateway-token': token }"
              :file-list="fileList"
              list-type="picture"
              :on-error="handleFailed"
              :limit="1"
            >
              <el-button
                icon="Paperclip"
                size="small"
                class="tool-btn"
                :disabled="fileList.length > 0"
                >{{ $t('text_upload_annex') }}</el-button
              >
            </el-upload>
          </el-tab-pane>
        </el-tabs>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit" :loading="loadState" :disabled="loadState">{{
        $t('text_confirm')
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import { useUserInfoStore } from '@/stores';
const token = computed(() => useUserInfoStore().userTocken);
import EmojiPicker from 'vue3-emoji-picker';
import type {
  FormInstance,
  UploadInstance,
  FormRules,
  UploadUserFile,
  TabsPaneContext,
} from 'element-plus';
import { pathMessage, sedPathFile } from '@/api/chatting';
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage,
    ElMessageBox,
  },
});
interface MessageProps {
  visible: boolean;
  ticketIds: number[];
  paramsData?: Record<string, unknown>;
}

interface Form {
  // uid_list: number[]
  inputValue: string;
  content?: string;
  file_url?: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<MessageProps>(), {
  visible: false,
  paramsData: () => ({}),
  ticketIds: () => [],
  content: '',
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const arrayLength = ref(0);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  content: '',
  inputValue: '',
  file_url: '',
});
// const rules = reactive<FormRules>({
//   content: [{ required: true, message: $t('text_please_batch_message'), trigger: 'blur' }]
// })
const loadState = ref(false);
const close = () => {
  value.value = false;
  // props.paramsData.bot_id = []
};

const activeName = ref<string>('first');
const handleTabClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name as string;
};
const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    // if(form.inputValue.split(',').map(Number).length > 50) {
    //   ElMessage.error($t('text_select_uid_account_max_fifty'))
    //   return
    // }
    loadState.value = true;
    try {
      if (activeName.value === 'first') {
        if (!form.content) {
          ElMessage.error($t('text_please_enter_the_text content'));
          loadState.value = false;
          return;
        }
        await pathMessage({
          project: props.paramsData.project,
          uid_list: form.inputValue.split(',').map(Number),
          content: form.content,
        })
          .then((res: any) => {
            ElMessage.success($t('text_patch_message_success'));
          })
          .finally(() => {
            loadState.value = false;
          });
      } else {
        if (!form.file_url) {
          ElMessage.error($t('text_please_update_files'));
          loadState.value = false;
          return;
        }
        await pathMessage({
          project: props.paramsData.project,
          uid_list: form.inputValue.split(',').map(Number),
          file_url: form.file_url,
        })
          .then((res: any) => {
            ElMessage.success($t('text_patch_message_success'));
          })
          .finally(() => {
            loadState.value = false;
          });
      }
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    }
  });
};

const upload = ref<UploadInstance>();
const fileList = ref<UploadUserFile[]>([]);
const handleFailed = () => {
  ElMessage.error('上传失败！');
  upload.value!.clearFiles();
};

const handleSuccess = (response: { data: { url: string } }, file: { name: string }) => {
  form.file_url = response.data.url;

  fileList.value.push({
    name: file.name,
    url: response.data.url,
  });
};
const handleRemove = (file: { url: string }) => {
  // 移除文件时，如果是图片释放对象URL
  URL.revokeObjectURL(file.url);
  form.file_url = '';
  fileList.value = [];
};

const addEmoji = (emoji: any) => {
  form.content += emoji.i;
};

const handleInput = (val: any) => {
  arrayLength.value = form.inputValue.split(',').map(Number).length;
  form.inputValue = val.replace(/，/g, ',');
};
onMounted(() => {
  // form.uid_list = props.ticketIds
  form.inputValue = props.ticketIds.join(',');
  arrayLength.value = props.ticketIds.length;
});
</script>

<style lang="scss" scoped>
:deep(.el-tabs) {
  width: 100%;
}
.message-box {
  display: flex;
  position: absolute;
  right: 5px;
  bottom: 0;
  &:deep(.el-button) {
    border: 0;
    font-size: 18px;
    &:hover {
      background-color: transparent;
    }
  }
}
.upload-progress {
  width: 160px;
  height: 20px;
  &:deep(.el-progress__text) {
    min-width: 0;
  }
}
</style>
