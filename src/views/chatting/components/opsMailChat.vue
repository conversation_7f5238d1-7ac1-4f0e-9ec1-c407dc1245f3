<template>
  <div class="chat-box">
    <!-- 处理文字聊天内容 -->
    <div class="chat-content-box">
      <div class="mouse-wheel-pulldown">
        <div ref="refScroll" class="pulldown-wrapper">
          <div class="pulldown-content">
            <div class="pulldown-tips">
              <div v-show="beforePullDown">
                <span>{{ $t('text_loading_more') }}</span>
              </div>
              <div v-show="!beforePullDown">
                <div v-show="isPullingDown">
                  <span>{{ $t('text_loading_tip') }}</span>
                </div>
                <div v-show="!isPullingDown"><span>{{ $t('text_loading_success') }}</span></div>
              </div>
            </div>
            <div class="dc-dialog-scroll-wrapper" v-if="messageList.length > 0">
              <mailRenderer v-for="item in messageList"
                :key="item.message_id"
                @reference-click="referenceClickHandle"
                @checkbox-change="checkboxChangeHandle"
                @menu-click="handleMenuClick"
                class="pulldown-list-item"
                :dialog-data="item"
                :id="'Msg' + item.message_id" />
            </div>
            <el-empty v-else :description="$t('info_no_data')" />
          </div>
        </div>
      </div>
    </div>
    <!-- 创建沟通记录 -->
    <div class="btn-box" v-show="showRecords">
      <el-button type="info" :disabled="checkList.length === 0" @click="handleCreateRecords">{{ $t('text_create_communication_records') }}</el-button>
      <span class="close-btn"><el-icon @click="handleClose">
          <Close />
        </el-icon></span>
    </div>
    <!-- 单条消息回复 -->
    <div class="replay-box" v-show="showReplyMsg">
      <span class="close-btn">
        <el-icon @click="handleCloseReply">
          <CircleClose />
        </el-icon>
      </span>
      <span class="message-content" v-if="replyContent !== ''">{{ $t('btn_reply') }} {{ replyAuthor }}:<span class="ml-3">{{ replyContent }}</span></span>
      <span class="message-content" v-else-if="replyContent === ''">{{ $t('btn_reply') }} {{ replyAuthor }}:<span class="ml-3">{{ replyAttach ? replyAttach : replyPoll }}</span></span>
    </div>
    <!-- 粘贴图片放置区 -->
    <div class="paste-box" v-if="imageUrl.length">
      <div v-for="(i, k) in imageUrl" :key="k" class="paste-img">
        <el-image :src="i" :preview-src-list="imageUrl" :initial-index="k" hide-on-click-modal preview-teleported style="width: 150px;height: 100px;"/>
        <el-button icon="DeleteFilled" size="small" class="del-btn" @click="delPasteImg(k)"/>
      </div>
    </div>
    <div class="chat-input-box">
      <el-input ref="textInput" id="chat-input" :readonly="sendMsgLoading" autosize
        v-model="inputContent" type="textarea" @input="updateCursorPosition" @click="updateCursorPosition"
        @keyup="updateCursorPosition"
        @paste="pasteImg"
        :disabled="partnerInfo.follow_status === 2"
        :placeholder="$t('text_send_to') + (replyAuthor ? replyAuthor : props.partnerInfo.display_name)" />
      <div class="tool-box">
        <!-- 附件功能 -->
        <el-upload ref="upload" class="pic-upload" :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :action="`/gateway/proxy/ops-ticket-api/all/api/line/channel/send_file`" :on-progress="handleProgress"
          :on-success="handleSuccess"
          :headers="customHeaders"
          :on-error="handleFailed" :limit="1">
          <el-tooltip placement="top" effect="light" :visible="uploadLoading">
            <template #content>
              <div class="upload-progress">
                <el-progress :percentage="uploadProgress" :stroke-width="15" :status="uploadSuccess ? 'success' : ''"
                  striped striped-flow :duration="uploadSuccess ? 0 : 10" />
              </div>
            </template>
            <el-button :disabled="partnerInfo.follow_status === 2" icon="Paperclip" size="small" class="tool-btn" :loading="uploadLoading" />
          </el-tooltip>
        </el-upload>
        <!-- 表情功能 -->
        <!-- <el-popover :width="'auto'"
          popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 0px; background: transparent;border-radius:8px;">
          <template #reference>
            <div class="emoji-btn tool-btn">
              <svg t="1720582777160" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="4323" width="17" height="17">
                <path
                  d="M512 0C229.2 0 0 229.2 0 512s229.2 512 512 512 512-229.2 512-512S794.8 0 512 0z m0 960C265 960 64 759 64 512S265 64 512 64s448 201 448 448-201 448-448 448z"
                  p-id="4324" fill="currentColor"></path>
                <path d="M320 405.3m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z" p-id="4325" fill="currentColor"></path>
                <path d="M704 405.3m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z" p-id="4326" fill="currentColor"></path>
                <path d="M512 810.7c117.8 0 213.3-95.5 213.3-213.3H298.7c0 117.8 95.5 213.3 213.3 213.3z" p-id="4327"
                  fill="currentColor"></path>
              </svg>
            </div>
          </template>
          <template #default>
            <EmojiPicker @select="addEmoji" :disable-sticky-group-names="true"
              :static-texts="{ placeholder: $t('text_search_emoji'), skinTone: $t('text_skin_tone') }"
              :group-names="{ recent: $t('text_recent'), smileys_people: $t('text_peple'), animals_nature: $t('text_nature'), food_drink: $t('text_food'), activities: $t('text_activities'), travel_places: $t('text_travel'), objects: $t('text_objects'), symbols: $t('text_symbols'), flags: $t('text_flags') }"
              :display-recent="true" />
          </template>
        </el-popover> -->
        <!-- 发送消息功能 -->
        <el-button icon="Promotion" @click="sendMessageClick" size="small" class="tool-btn send-btn"
          :loading="sendMsgLoading" :disabled="!sendContent && !imageUrl.length" />
      </div>
      <!-- 未读消息按钮 -->
      <el-button v-if="showNewMsgBtn" type="primary" icon="ArrowDownBold" size="small" plain class="unread-btn"
        @click="scrollBottom">{{ $t('btn_not_read') }}</el-button>
      <!-- 快速触底按钮 -->
      <el-button v-if="showGoBottomBtn && !showNewMsgBtn" circle icon="ArrowDownBold" size="small" plain
        class="unread-btn" @click="scrollBottom" />
    </div>
    <!-- 创建沟通记录 -->
    <createDrawer v-if="createVisible" v-model:visible="createVisible" :params-data="partnerInfo" :msg-ids="msgIds" @success="handleClose"></createDrawer>
  </div>
</template>

<script lang="ts">
import EmojiPicker from 'vue3-emoji-picker'
import 'vue3-emoji-picker/css'
import { mailMessageList, mailSendMsg, mailNewMessage } from '@/api/mail'
// import { discordReplyMessage } from '@/api/chatting'
import { useDebounceFn } from '@vueuse/core'
import createDrawer from './createDrawer.vue'
import type { UploadInstance } from 'element-plus'
import { useUserInfoStore } from '@/stores'
import { useI18n } from 'vue-i18n'
interface PartnerInfo {
  follow_status: number // 玩家是否把你删除 1:未删除 2:已删除
  bot_id: string // 机器人id
  message_id: string
  channel_id: string // 会话id
  line_user_id: string // 用户id
  global_name: string // 用户名
  guild_id: string // 公会id
  processor: string // 处理人
  project: string // 游戏
  total_pay: number // 总充值
  display_name: string // 玩家名
  vip_level: number // vip等级
  uid?: number
  sid: string // 服务器
  pay_all: number // 玩家累计付费金额
}
interface ChatProps {
  partnerInfo: PartnerInfo
}
interface Message {
  attach: any[] // 附件
  author: {
    avatar: string // 头像
    bot: boolean // 是否机器人
    global_name: string // 用户名
    id: string // 用户id
    username: string // 用户名
  }
  channel_id: string // 会话id
  content: string // 文字内容
  created_at: string // 创建时间
  embed: any[] // 嵌入
  from_user_id: string // 发送者id
  is_edited: boolean // 是否编辑过
  checked: boolean // 是否展示复选框
  message_id: string // 消息id
  project: string // 游戏
  reactions: any[] // 表情
  referenced_msg?: Message // 引用消息
  referenced_msg_id: string // 引用消息id
  stickers: any[] // 贴纸
  tick_time: number // 消息时间戳
}
interface Reaction {
  name: string // 表情内容
  user_id: string // 用户id
  created_at: string // 创建时间
  message_id: string // 消息id
}
interface DcEvent {
  event_type: number // 事件类型 0: 未知 1：新增消息 2：编辑消息 3：删除消息 4：新增反应 5：删除反应
  msg_detail: Message // 消息详情
  del_msg_ids: string[] // 删除的消息id
  add_reaction: Reaction[] // 新增的反应
  del_reaction: Reaction[] // 删除的反应
}
export default defineComponent({
  name: 'opsLineChat',
  components: {
    createDrawer,
    ElMessage,
    ElNotification,
    EmojiPicker
  }
})
</script>
<script setup lang="ts">
import BScroll from '@better-scroll/core'
import PullDown from '@better-scroll/pull-down'
import MouseWheel from '@better-scroll/mouse-wheel'
import mailRenderer from './opsMailRenderer.vue'
const { t: $t } = useI18n()
const token = computed(() => useUserInfoStore().userTocken)


const cursorPosition = ref(null)
const textInput = ref(null) as any
const updateCursorPosition = () => {
  const inputElement = textInput.value?.$refs.textarea
  if (inputElement) {
    cursorPosition.value = inputElement.selectionStart
  }
}

let customHeaders = ref<any>({})
const handleBeforeUpload = (file: any) => {
  if (file.type.split("/")[0] === 'image') {
    // line官方只支持jpeg和png两种格式图片，最大不超过10MB
    if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
      ElMessage.error($t('text_error_image'))
      return false
    } else if (file.size / 1024 / 1024 > 10) {
      ElMessage.error($t('text_error_image_size'))
      return false
    }
  } else if (file.type.split("/")[0] === 'video') {
    // line官方约定视频只支持mp4格式，最大不超过200MB
    if (file.type !== 'video/mp4') {
      ElMessage.error($t('text_error_video'))
      return false
    } else if (file.size / 1024 / 1024 > 200) {
      ElMessage.error($t('text_error_video_size'))
      return false
    }
  } else if (file.type.split("/")[0] === 'audio') {
    // line官方约定音频只支持mp3格式，最大不超过5MB
    if (file.type !== 'audio/mp3') {
      ElMessage.error($t('text_error_audio'))
      return false
    } else if (file.size / 1024 / 1024 > 5) {
      ElMessage.error($t('text_error_audio_size'))
      return false
    }
  } else {
    ElMessage.error($t('text_error_file'))
    return false
  }
  customHeaders.value = {
    'admin-gateway-token': token.value,
    'file-type': file.type.split("/")[0],   // image/video/audio
    'format': file.type.split("/")[1].toLowerCase(),  // 文件格式后端必须小写 jpeg,png,mp4,mp3等
    'channel-id': props.partnerInfo.channel_id,
    'bot-id': props.partnerInfo.bot_id,
    'line-user-id': props.partnerInfo.line_user_id, 
  }
  return true
}

// 粘贴图片
const imageUrl = reactive<string[]>([])
const fileList: any = []
const pasteImg = (e: ClipboardEvent) => {
  const clipboardData = e.clipboardData
  if (clipboardData && clipboardData.items) {
    const item = clipboardData.items[0]
    if (item.kind === 'file' && item.type.startsWith('image/')) {
      const file = item.getAsFile()
      if (file) {
        fileList.push(file) // 上传用
        const imgUrl = URL.createObjectURL(file)
        imageUrl.push(imgUrl) // 预览用
      }
    }
  } else {
    ElMessage.error('无法从剪贴板获取数据')
  }
}
// 点击删除具体图片
const delPasteImg = (k: number) => {
  imageUrl.splice(k, 1)
  fileList.splice(k, 1)
}
const uploadImg = (file: any) => {
  const formdata = new FormData()
  formdata.append('file', file)
  sendMsgLoading.value = true
  imageUrl.length = 0  // 清空预览
  fetch(`/gateway/proxy/ops-ticket-api/all/api/line/channel/send_file`, {
    method: 'post',
    headers: {
      'admin-gateway-token': token.value,
      'file-type': (file.type).split("/")[0],   // image/video/audio
      'format': file.type.split("/")[1].toLowerCase(),  // 文件格式后端必须小写 jpeg,png,mp4,mp3等
      'channel-id': props.partnerInfo.channel_id,
      'bot-id': props.partnerInfo.bot_id,
      'line-user-id': props.partnerInfo.line_user_id, 
    },
    body: formdata
  }).finally(() => sendMsgLoading.value = false)
}

const addEmoji = (emoji: any) => {
  const inputElement = textInput.value?.$refs.textarea
  if (cursorPosition.value !== null) {
    const beforeText = inputContent.value.slice(0, cursorPosition.value)
    const afterText = inputContent.value.slice(cursorPosition.value)
    inputContent.value = beforeText + emoji.i + afterText

    const newCursorPosition = cursorPosition.value + emoji.i.length
    nextTick(() => {
      inputElement.focus()
      inputElement.setSelectionRange(newCursorPosition, newCursorPosition)
      updateCursorPosition()
    })
  } else {
    // 如果没有获取到光标位置，则在末尾添加 emoji
    inputContent.value += emoji.i
  }
}
// props
const props = withDefaults(defineProps<ChatProps>(), {
  partnerInfo: () => ({
    follow_status: 1,
    bot_id: '',
    message_id: '',
    channel_id: '',
    line_user_id: '',
    global_name: '',
    guild_id: '',
    processor: '',
    project: '',
    total_pay: 0,
    display_name: '',
    vip_level: 0,
    sid: '',
    pay_all: 0
  })
})

// 轮询接口心跳开关
const heartbeat = ref(true)

// BS相关
BScroll.use(PullDown)
BScroll.use(MouseWheel)
const refScroll = ref<HTMLElement | null>()
const observer = ref<ResizeObserver | null>()
const scroll = ref<BScroll | null>(null)
const beforePullDown = ref(true)
const isPullingDown = ref(false)
const autoScrollMark = ref(true)
const showGoBottomBtn = ref(false)
const activeTab = inject('activeTab') as Ref<number>

// 监听activeTab变化，切换回聊天tab时拉取新消息，更新消息列表滚动到底部
watch(() => activeTab.value, (newVal) => {
  if (newVal === 1) {
    heartbeat.value = true
    getMessageEvent()
  } else if (newVal === 2) {
    heartbeat.value = false
  }
})

const initScroll = () => {
  scroll.value = new BScroll(refScroll.value!, {
    scrollY: true,
    bounceTime: 800,
    probeType: 3,
    mouseWheel: {
      speed: 30,
      invert: false,
    },
    disableMouse: true,
    preventDefault: false,
    pullDownRefresh: {
      threshold: 20,
      stop: 56
    }
  })
  // scroll.value.on('pullingDown', getHistoryMessage)
  let lastScrollY = 0
  scroll.value.on('scroll', (pos: any) => {
    // 下拉加在历史消息，不用BS自带的下拉刷新，对触摸板支持不好，有BUG，自己实现
    if (!(pos.y < 0) && !isPullingDown.value) {
      getHistoryMessage()
    }
    
    if (activeTab.value === 2) return // 避免tab切换时display为none时触发

    heartbeat.value = false
    if (pos.y > lastScrollY) {
      autoScrollMark.value = false
    }

    if (pos.y - scroll.value!.maxScrollY > 500) {
      showGoBottomBtn.value = true
    } else {
      showGoBottomBtn.value = false
    }
    
    lastScrollY = pos.y
  })

  scroll.value.on('scrollEnd', () => {
    if (activeTab.value === 2) return // 避免tab切换时display为none时触发

    heartbeat.value = true
    if (scroll.value!.y === scroll.value!.maxScrollY || scroll.value!.y < scroll.value!.maxScrollY) {
      autoScrollMark.value = true
      showNewMsgBtn.value = false
      showGoBottomBtn.value = false
    }
  })
}

// 点击引用消息，滚动到引用消息位置
const referenceClickHandle = (msgId: string) => {
  const target = document.getElementById('Msg' + msgId)

  if (target) {
    scroll.value!.scrollToElement(target, 500, false, false)
    setTimeout(() => {
      target.classList.add('pulse')
    }, 500)
    setTimeout(() => {
      target.classList.remove('pulse')
    }, 1500)
  } else {
    ElMessage.warning($t('text_msg_noload'))
  }
}
// 获取复选框选中的消息记录
const checkList = ref<string[]>([])
const msgIds = ref('')
const checkboxChangeHandle = (val: string) => {
  const index = checkList.value.indexOf(val)
  if (index === -1) {
    checkList.value.push(val)
  } else {
    // 如果 val 已在数组中，则移除
    checkList.value.splice(index, 1);  
  }
  msgIds.value = checkList.value.join()
}
// 对话消息按钮操作事件
const showRecords = ref(false)
const showReplyMsg = ref(false)
const replyAuthor = ref('')
const replyContent = ref('')
const replyAttach = ref('')
const replyPoll = ref('')
const changeMsgId = ref('')
const handleMenuClick = (val: string, dialogData: any) => {
  if(val === '1') {
    messageList.value.forEach((item) => {
      item.checked = true
    })
    showRecords.value = true
    showReplyMsg.value = false
    replyAuthor.value = ''
    replyContent.value = ''
    replyAttach.value = ''
    replyPoll.value = ''
  } else if(val === '2') {
    const index = messageList.value.findIndex(item => item.message_id === dialogData.message_id)
    messageList.value.forEach((item: any, idx) => {
      if(idx !== index) {
        item['showEditContent'] = false
      } else {
        item['showEditContent'] = true
      }
    })
    showReplyMsg.value = false
    replyAuthor.value = ''
    showRecords.value = false
  } else if(val === '3') {
    showReplyMsg.value = true
    replyAuthor.value = dialogData.author.username
    replyContent.value = dialogData.content.replace(/<br>+/g, '')
    changeMsgId.value = dialogData.message_id
    dialogData.attach.forEach(((item: any) => {
      replyAttach.value = item.filename
    }))
    replyPoll.value = dialogData.poll?.question_text
  }
}
// 关闭回复
const handleCloseReply = () => {
  showReplyMsg.value = false
  replyAuthor.value = ''
}
// 创建沟通记录
const createVisible = ref<boolean>(false)
const handleCreateRecords = () => {
  createVisible.value = true
}
// 关闭沟通任务
const handleClose = () => {
  showRecords.value = false
  checkList.value = []
  messageList.value.forEach((item) => {
    item.checked = false
  })
}
// 获取聊天记录
const messageList = ref<Message[]>([])
const getInitMessage = () => {
  mailMessageList({
    channel_id: props.partnerInfo.channel_id,
    bot_id: props.partnerInfo.bot_id,
    line_user_id: props.partnerInfo.line_user_id
  }).then((res: any) => {
    messageList.value = res.data
    nextTick(() => {
      scroll.value!.refresh()
      // 滚动到最底部
      if (messageList.value.length < 1) return
      scroll.value!.scrollToElement('#Msg' + messageList.value[messageList.value.length - 1].message_id, 0, false, false)
    })
  })
}

// 监听props.partnerInfo变化，拉取聊天记录
watch(() => props.partnerInfo, (newVal) => {
  getInitMessage()
}, {
  // 首次拉取聊天记录
  immediate: true
})

// 获取新消息、事件(用于轮询)
const showNewMsgBtn = ref(false)
const getMessageEvent = () => {
  mailNewMessage({
    channel_id: props.partnerInfo.channel_id,
    bot_id: props.partnerInfo.bot_id,
    line_user_id: props.partnerInfo.line_user_id,
    tick_time: messageList.value[messageList.value.length - 1].tick_time
  }).then((res: any) => {
    res.forEach((item: Message) => {
      if (!messageList.value.some(msg => msg.message_id === item.message_id)) {
        messageList.value.push(item)
        if (!autoScrollMark.value) {
          showNewMsgBtn.value = true
          ElNotification.success({
            title: $t('text_have_unread'),
            message: item.content.length > 20 ? item.content.slice(0, 20) + '...' : item.content,
            showClose: false,
          })
        }
      }
    })
    // res.fresh_event.forEach((event: DcEvent) => {
    //   if (event.event_type === 2) {
    //     const index = messageList.value.findIndex(msg => msg.message_id === event.msg_detail.message_id)
    //     if (index !== -1) {
    //       messageList.value[index].is_edited = event.msg_detail.is_edited
    //       messageList.value[index].content = event.msg_detail.content
    //       messageList.value[index].attach = event.msg_detail.attach
    //       messageList.value[index].embed = event.msg_detail.embed
    //       messageList.value[index].stickers = event.msg_detail.stickers
    //     }
    //   } else if (event.event_type === 3) {
    //     messageList.value = messageList.value.filter(msg => !event.del_msg_ids.includes(msg.message_id))
    //   } else if (event.event_type === 4) {
    //     const index = messageList.value.findIndex(msg => msg.message_id === event.add_reaction[0].message_id)
    //     if (index !== -1) {
    //       messageList.value[index].reactions = [...messageList.value[index].reactions, ...event.add_reaction]
    //     }
    //   } else if (event.event_type === 5) {
    //     const index = messageList.value.findIndex(msg => msg.message_id === event.del_reaction[0].message_id)
    //     if (index !== -1) {
    //       messageList.value[index].reactions = messageList.value[index].reactions.filter(reaction => {
    //         return !event.del_reaction.some(del => del.name === reaction.name)
    //       })
    //     }
    //   }
    // })

    nextTick(() => {
      scroll.value!.refresh()
      // 滚动到最底部
      if (autoScrollMark.value) {
        scroll.value!.scrollToElement('#Msg' + messageList.value[messageList.value.length - 1].message_id, 0, false, false)
      }
    })
  })
}
// 未读消息滚动底部功能
const scrollBottom = () => {
  scroll.value!.scrollTo(0, scroll.value!.maxScrollY, 500)
  setTimeout(() => {
    showNewMsgBtn.value = false
    showGoBottomBtn.value = false
  }, 500)
}

// 拉取历史消息
const getHistoryMessage = () => {
  beforePullDown.value = false
  isPullingDown.value = true
  heartbeat.value = false
  const lastMsgId = messageList.value[0].message_id
  mailMessageList({
    channel_id: props.partnerInfo.channel_id,
    bot_id: props.partnerInfo.bot_id,
    line_user_id: props.partnerInfo.line_user_id,
    before: true,  // 是否要向上翻页
    tick_time: messageList.value[0].tick_time  // 获取该时间戳之前的消息，before为true时必传
  }).then((res: any) => {
    messageList.value.unshift(...res.data)
    nextTick(() => {
      scroll.value!.refresh()
      scroll.value!.scrollToElement('#Msg' + lastMsgId, 0, false, false)
    })
  }).finally(() => {
    isPullingDown.value = false
    heartbeat.value = true
    setTimeout(() => {
      scroll.value!.finishPullDown()
    }, 500)
    setTimeout(() => {
      beforePullDown.value = true
    }, 800)
  })
}

const isEmptyQuillContent = (html: string) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, "text/html")
  const text = doc.body.textContent || ""
  const images = doc.getElementsByTagName('img')
  let hasImageContent = false

  for (let i = 0; i < images.length; i++) {
    if (images[i].src.trim() !== '') {
      hasImageContent = true
      break
    }
  }

  return text === "" && !hasImageContent
}
// 用户输入的内容
const inputContent = ref('')
//被格式化后用户输入的内容，用于判断非空和发送
const sendContent = computed(() => {
  if (isEmptyQuillContent(inputContent.value)) {
    return ''
  } else {
    return inputContent.value
  }
})
// 发送消息
const sendMsgLoading = ref(false)
const sendMessage = () => {
  if (sendContent.value === '') {
    return
  }
  if (sendMsgLoading.value) {
    return
  }
  sendMsgLoading.value = true
  if(showReplyMsg.value) {
    // discordReplyMessage({
    //   channel_id: props.partnerInfo.channel_id,
    //   bot_id: props.partnerInfo.bot_id,
    //   message_id: changeMsgId.value,
    //   content: sendContent.value
    // }).then(() => {
    //   inputContent.value = ''
    //   autoScrollMark.value = true // 发送消息后自动滚动到底部
    //   showReplyMsg.value = false
    //   ElMessage.success($t('text_success'))
    //   getMessageEvent()
    // }).finally(() => {
    //   sendMsgLoading.value = false
    // })
  } else {
    mailSendMsg({
      channel_id: props.partnerInfo.channel_id,
      bot_id: props.partnerInfo.bot_id,
      line_user_id: props.partnerInfo.line_user_id,
      content: sendContent.value,
    }).then(() => {
      inputContent.value = ''
      autoScrollMark.value = true // 发送消息后自动滚动到底部
      getMessageEvent()
    }).finally(() => {
      sendMsgLoading.value = false
    })
  }
}
// const sendMessageEnter = (event: any) => {
//   if (event.key === 'Enter') {
//     if (!event.shiftKey) {
//       event.preventDefault()
//       sendMessage()
//     }
//   }
// } 
const sendMessageClick = () => {
  sendMessage()
  while (fileList.length > 0) {
    uploadImg(fileList.shift())  // 发送并移除该张图片
  }
}

let timer: any = null // 定时器
const isTabActive = ref(document.visibilityState === 'visible') // 系统是否在激活状态
onMounted(() => {
  // 监听系统是否在激活状态
  document.addEventListener('visibilitychange', () => {
    isTabActive.value = document.visibilityState === 'visible'
  })
  // 初始化bs并监听resize
  if (!refScroll.value) return
  initScroll()
  observer.value = new ResizeObserver(entries => {
    debounceFn()
  })
  const debounceFn = useDebounceFn(() => {
    scroll.value!.refresh()
  }, 200)
  observer.value.observe(refScroll.value)
  
  // 添加定时器，每秒拉取新消息
  if (timer) return
  timer = setInterval(() => {
    if (!heartbeat.value) return
    getMessageEvent()
  }, 3000)
})
// 销毁定时器
onBeforeUnmount(() => {
  clearInterval(timer)
  timer = null
  if (observer.value) {
    observer.value.disconnect()
  }
  document.removeEventListener('visibilitychange', () => {
    isTabActive.value = document.visibilityState === 'visible'
  })
})


const upload = ref<UploadInstance>()
const uploadLoading = ref(false)
const uploadProgress = ref(0)
const uploadSuccess = ref(false)
const handleFailed = () => {
  ElMessage.error('上传失败！')
  uploadProgress.value = 0
  uploadSuccess.value = false
  uploadLoading.value = false
  upload.value!.clearFiles()
}
const handleProgress = (event: any) => {
  uploadLoading.value = true
  uploadSuccess.value = false
  uploadProgress.value = Math.min(Math.floor(event.percent), 99)
}
const handleSuccess = () => {
  uploadSuccess.value = true
  uploadProgress.value = 100
  setTimeout(() => {
    uploadLoading.value = false
    upload.value!.clearFiles()
  }, 600)
}
</script>

<style lang="scss" scoped>
.chat-box {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 0;
  flex: 1 1 auto;
  height: 100%;
  .chat-content-box {
    flex: 1 1 auto;
    overflow: auto;
    .mouse-wheel-pulldown {
      height: 100%;
      .pulldown-wrapper {
        position: relative;
        height: 100%;
        padding: 0 10px 0 0;
        // border: 1px solid #c0c4cc;
        border-radius: 8px;
        overflow: hidden;
        .pulldown-content {
          box-sizing: border-box;
          padding-top: 1px;
          padding-bottom: 1px; 
          .pulldown-list-item {
            -webkit-animation-duration: 1s;
            animation-duration: 1s;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            transform-origin: left top;
            border-radius: 8px;
          }
        }
      }
      .pulldown-tips {
        position: absolute;
        width: 100%;
        padding: 20px;
        box-sizing: border-box;
        transform: translateY(-100%) translateZ(0);
        text-align: center;
        color: #999;
        font-size: 12px;
      }
    }
  }
  .paste-box {
    flex: 1 0 auto;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    .paste-img {
      border: 1px solid #dfdcdc;
      position: relative;
      .del-btn {
        width: 20px;
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
  .chat-input-box {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: end;
    // margin-top: 10px;
    padding: 4px 2px;
    border: 1px solid #c0c4cc;
    border-radius: 8px;
    background-color: #fff;
    .unread-btn {
      position: absolute;
      right: 0px;
      top: -50px;
      z-index: 999;
      border-radius: 15px;
    }
    .tool-box {
      width: 100px;
      flex-shrink: 0;
      margin-right: 2px;
      line-height: 30px;
      text-align: right;
      .pic-upload {
        display: inline-block;
        font-size: 18px;
        margin: 0;
        height: 31px;
        overflow: hidden;
        float: right;
      }
      .tool-btn {
        border: none;
        font-size: 18px;
        margin: 0;
        height: 32px;
        float: right;
        padding: 2px 5px;
        color:#606266;
      }
      .tool-btn:hover {
        background-color: inherit !important;
        color: #4aa181;
      }
      .is-disabled.send-btn {
        color: #606266 !important;
      }
      .send-btn {
        background-color: inherit !important;
        color: #4aa181 !important;
      }
      .emoji-btn {
        cursor: pointer;
        svg {
          margin-top: 6px;
        }
      }
    }
    .el-textarea {
      flex-grow: 1;
    }
    &:deep(#chat-input) {
      box-shadow: none !important;
      max-height: 40vh;
      resize: none;
      &:hover {
        box-shadow: none !important;
      }
    }
  }
  .btn-box {
    width: 100%;
    background-color: #fff;
    border: 1px solid #c0c4cc;
    border-radius: 8px;
    padding: 5px;
    position: absolute;
    bottom: 0;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    .close-btn {
      position: absolute;
      right: 10px;
      top: 14px;
      cursor: pointer;
    }
  }
  .replay-box {  
    display: flex;
    align-items: center;
    margin-bottom: 3px;
    padding: 4px;
    background-color: #f2f3f5;
    border-radius: 5px;
    font-size: 15px;
    color: #9fa3a8;
    .close-btn {  
      margin: 0 5px;
      padding-right: 5px;
      line-height: 10px;
      border-right: 1px solid #dfe0e1;
      cursor: pointer;
    }
    .message-content {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      line-clamp: 1;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      .ml-3 {
        margin-left: 3px;
      }
    }
  }
}
.upload-progress {
  width: 160px;
  height: 20px;
  &:deep(.el-progress__text) {
    min-width: 0;
  }
}
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
  background: #edf6f2;
}
.v3-emoji-picker {
  &:deep(.v3-body-inner) {
    scrollbar-color: auto;
    scrollbar-width: auto;
  }
}
</style>