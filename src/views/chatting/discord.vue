<template>
  <splitpanes class="split-box default-theme" vertical :push-other-panes="false">
    <!-- 左侧内容区域 -->
    <pane size="13.5">
      <splitpanes class="split-box o-left default-theme" horizontal :push-other-panes="false">
        <!-- 数据概览 -->
        <pane>
          <div class="title">
            <span class="iconfont icon-shujugailan"></span>
            <span class="text">{{ $t('text_data_overview') }}</span>
          </div>
          <el-scrollbar>
            <el-button v-for="(v, k) in ovButtonList" @click="shortcutHandle(v)"
              :class="[v.id === activeShortcutId ? 'activeBtn' : '']" :key="k">
              {{ $t(v.lable) }}: {{ overviewData[v.prop] }}
            </el-button>
          </el-scrollbar>
        </pane>
        <!-- 自定义tab数据概览 -->
        <pane>
          <div class="title">
            <span class="iconfont icon-duoweiduziyoupouxi"> </span>
            <span class="text">{{ $t('text_custom_data_overview') }}</span>
          </div>
          <el-scrollbar v-loading="tabsLoading">
            <el-menu v-if="openIndex.length > 0" :default-openeds="openIndex" class="project-sortable-tree">
              <template  v-for="(v, k) in tabList" :key="k">
                <el-sub-menu :index="v.project">
                  <template #title>
                    <DragIcon />
                    <span>{{ v.project }}</span>
                  </template>
                  <template v-for="(l, m) in v.tabs" :key="m">
                    <el-menu-item :index="l.tab_name" :data-id="l.id">
                      <el-button @click="handleClickTab(l)" :class="[l.id === activeTabId ? 'activeBtn' : '']" style="position: relative;">
                        <DragIcon />
                        {{ l.tab_name }}:{{ l.count }}
                        <el-link :disabled="l.operator !== userInfo.username" icon="Edit" :underline="false" class="edit-btn" @click.stop="handleEditTab(l)"></el-link>
                        <el-link :disabled="l.operator !== userInfo.username" icon="Delete" :underline="false" class="delete-btn" @click.stop="handleDelete(l)"></el-link>
                      </el-button>
                    </el-menu-item>
                  </template>
                </el-sub-menu>
              </template>
            </el-menu>
          </el-scrollbar>
        </pane>
      </splitpanes>
    </pane>
    <!-- 中间内容区域 -->
    <pane>
      <splitpanes class="split-box default-theme" horizontal :push-other-panes="false">
        <pane size="25">
          <el-scrollbar class="pane-wapper">
            <el-form size="small" ref="searchFormRef" :inline="true" :model="searchForm" class="search-form">
              <!-- 游戏 -->
              <el-form-item prop="project">
                <el-select v-model="searchForm.project" @change="changeGameHandle" :placeholder="$t('text_game')" multiple collapse-tags
                  collapse-tags-tooltip clearable filterable :reserve-keyword="false">
                  <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
                    :value="v.game_project"></el-option>
                </el-select>
              </el-form-item>
              <!-- 状态 -->
              <el-form-item prop="status">
                <el-select v-model="searchForm.status" :placeholder="$t('text_status')" multiple collapse-tags
                  collapse-tags-tooltip clearable>
                  <el-option v-for="(v, index) in enumList.DiscordServiceReplyStatus" :key="index" :label="v.name"
                    :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 玩家输入信息 - 模糊查询 -->
              <el-form-item prop="user_content" style="width: 55%;">
                <el-input v-model="searchForm.user_content" :placeholder="$t('text_player_content') " clearable />
              </el-form-item>
              <!-- 玩家备注信息 - 模糊查询 -->
              <el-form-item prop="user_detail_remark" style="width: 55%;">
                <el-input v-model="searchForm.user_detail_remark" :placeholder="$t('text_remark_content') " clearable />
              </el-form-item>
              <!-- 维护人 -->
              <el-form-item prop="processor">
                <el-select v-model="searchForm.processor" :placeholder="$t('text_maintainer')" :reserve-keyword="false" filterable multiple clearable collapse-tags>
                  <template #header>
                    <el-checkbox
                      v-model="checkProcessor"
                      :indeterminate="indeterminateProcessor"
                      @change="processorCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="(v, index) in csList" :key="index" :label="v.account" :value="v.account"></el-option>
                </el-select>
              </el-form-item>
              <!-- 最近处理人 -->
              <el-form-item prop="last_reply_service">
                <el-select v-model="searchForm.last_reply_service" :placeholder="$t('text_processd_by_person')" :reserve-keyword="false" filterable multiple clearable collapse-tags>
                  <template #header>
                    <el-checkbox
                      v-model="checkLast"
                      :indeterminate="indeterminateLast"
                      @change="lastCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="(v, index) in csList" :key="index" :label="v.account" :value="v.account"></el-option>
                </el-select>
              </el-form-item>
              <!-- 玩家昵称 -->
              <el-form-item prop="dsc_user_nickname">
                <el-input v-model="searchForm.dsc_user_nickname" :placeholder="$t('text_player_dc') " clearable />
              </el-form-item>
              <!-- DC机器人 -->
              <el-form-item prop="bot_ids">
                <el-select v-model="searchForm.bot_ids" :disabled="searchForm.project.length === 0" :placeholder="$t('text_bots')" multiple collapse-tags collapse-tags-tooltip clearable>
                  <template #header>
                    <el-checkbox
                      v-model="checkBotids"
                      :indeterminate="indeterminateBotids"
                      @change="botidsCheckAll"
                    >{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="(v, k) in botsList" :key="k" :label="v" :value="k"></el-option>
                </el-select>
              </el-form-item>
              <!-- 回复时间 -->
              <el-form-item prop="replied_at">
                <el-date-picker v-model="searchForm.replied_at as any" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" type="datetimerange" :range-separator="$t('to')"
                  :start-placeholder="$t('text_recovery_time')" :end-placeholder="$t('text_recovery_time')">
                </el-date-picker>
              </el-form-item>
              <!-- 最后登录时间 -->
              <el-form-item prop="replied_at">
                <el-date-picker v-model="searchForm.last_login as any" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" type="datetimerange" :range-separator="$t('to')"
                  :start-placeholder="$t('text_last_last_login_time')"
                  :end-placeholder="$t('text_last_last_login_time')">
                </el-date-picker>
              </el-form-item>
              <!-- 玩家DC ID -->
              <el-form-item prop="dsc_user_id">
                <el-input v-model="searchForm.dsc_user_id" :placeholder="$t('text_player_dcid')" clearable />
              </el-form-item>
              <!-- 玩家uid -->
              <el-form-item prop="uids">
                <el-input v-model="searchForm.uids" :placeholder="$t('text_user_uid')" clearable />
              </el-form-item>
              <!-- 玩家fpid -->
              <el-form-item prop="fpid">
                <el-input v-model="searchForm.fpid" :placeholder="$t('text_fpid')" clearable />
              </el-form-item>
              <!-- 玩家服务器 -->
              <el-form-item prop="sid">
                <el-input v-model="searchForm.sid" :placeholder="$t('text_player_sid')" clearable />
              </el-form-item>
              <!-- 30天累计付费金额 -->
              <el-form-item prop="pay_last_thirty_days">
                <el-input v-model.number="searchForm.pay_prev" :placeholder="$t('text_thirty_day_pay')" clearable @blur="getPayVal"
                  style="width: 50px;" /><span class="hor-line">-</span>
                <el-input v-model.number="searchForm.pay_next" :placeholder="$t('text_thirty_day_pay')"  clearable @blur="getPayNextVal"
                  style="width: 50px;" />
              </el-form-item>
              <!-- 累计付费金额 -->
              <el-form-item prop="pay_all">
                <el-input :placeholder="$t('text_total_pay')" v-model.number="searchForm.pay_all_prev" clearable @blur="getPayAllVal"
                  style="width: 50px;" /><span class="hor-line">-</span>
                <el-input :placeholder="$t('text_total_pay')" v-model.number="searchForm.pay_all_next" clearable @blur="getPayAllNextVal"
                  style="width: 50px;" />
              </el-form-item>
              <!-- VIP状态 -->
              <el-form-item prop="vip_state">
                <opsSelect v-model="searchForm.vip_state" :placeholder="$t('text_vip_state')" clearable>
                  <el-option v-for="(v, index) in enumList.PlayerVipState" :key="index" :label="v.name"
                    :value="v.value"></el-option>
                </opsSelect>
              </el-form-item>
              <!-- 标签条件 -->
              <el-form-item prop="tag_type">
                <el-select v-model="searchForm.tag_type" :placeholder="$t('text_please_change_tag_condition')"
                clearable :disabled="searchForm.project.length !== 1" @change="handleTagType">
                  <el-option v-for="(v, index) in enumList.TagType" :key="index" :label="v.name"
                    :value="v.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 标签 -->
              <el-form-item prop="tags">
                <el-cascader v-model="searchForm.tags" style="width:100%" :options="tagOpts" filterable
                  :disabled="searchForm.project.length !== 1 || !searchForm.tag_type || searchForm.tag_type === 4 || searchForm.tag_type === 1"
                  :placeholder="$t('text_tag_placeholder')"
                  popper-class="tagsCascaderClass"
                  @change="handleTagChange"
                  collapse-tags
                  collapse-tags-tooltip
                  :reserve-keyword="false"
                  :max-collapse-tags="1"
                  :props="cascaderProps" clearable>
                  <template #default="{ data }">
                    <span v-if="data.tag_name === 'all'">
                      <el-checkbox v-model="tagsAllSelected" @change="tagsHandleSelectAll">{{ $t('text_select_all') }}</el-checkbox>
                    </span>
                    <span v-else>{{ data.tag_name }}</span>
                  </template>
                </el-cascader>
              </el-form-item>
              <!-- 语言 -->
              <el-form-item prop="language">
                <el-select v-model="searchForm.language" :placeholder="$t('text_lang')" :reserve-keyword="false" clearable filterable multiple collapse-tags>
                  <template #header>
                    <el-checkbox v-model="checkLang" :indeterminate="indeterminateLang" @change="langCheckAll">{{ $t('text_select_all') }}</el-checkbox>
                  </template>
                  <el-option v-for="(v, index) in langList" :key="index" :label="v.name" :value="v.code"></el-option>
                </el-select>
              </el-form-item>
              <!-- 生日 -->
              <el-form-item prop="birthday">
                <el-date-picker
                  v-model="searchForm.birthday"
                  type="daterange"
                  start-placeholder="生日开始日期"
                  end-placeholder="生日结束日期"
                  :range-separator="$t('to')"
                  format="MM-DD"
                  value-format="MM-DD">
                </el-date-picker>
              </el-form-item>
              <!-- 搜索操作按钮 -->
              <el-form-item>
                <el-button type="primary" plain icon="Search" @click="search">{{ $t('btn_search') }}</el-button>
              </el-form-item>
              <!-- 重置按钮 -->
              <el-form-item>
                <el-button icon="Refresh" @click="reset">{{ $t('btn_reset') }}</el-button>
              </el-form-item>
              <!-- 下载按钮 -->
              <el-form-item>
                <el-button type="primary" icon="Download" @click="donwLoad" v-has="'discord:download'"
                  :loading="progState" :disabled="progState">{{ $t('btn_download') }}
                  <span v-if="progState">{{ progressNum + '%' }}</span>
                </el-button>
              </el-form-item>
              <!-- 批量私信 -->
              <el-form-item>
                <el-button type="primary" icon="ChatLineRound" @click="handleAllCheckData" :disabled="checkDataList.length === 0">{{ $t('text_batch_private_message') }}</el-button>
              </el-form-item>
              <!-- 固定tab -->
              <el-form-item>
                <el-button type="primary" icon="AddLocation" :disabled="!isObjectNonEmpty(searchForm)" @click="handleAddTabData">{{ $t('text_fixed_tab') }}</el-button>
              </el-form-item>
              <!-- 批量打标签 -->
              <el-form-item>
                <el-button type="primary" icon="PriceTag" @click="handlePatchTag" :disabled="checkUserIdList.length === 0">{{ $t('btn_batch_mark') }}</el-button>
              </el-form-item>
              <!-- 批量删除标签 -->
              <el-form-item>
                <el-button type="primary" @click="batchDelTagHandle" v-has="'discord:batchDeleteTag'" icon="Delete"
                :disabled="checkUserIdList.length === 0">{{ $t('text_batch_del_tag') }}</el-button>
              </el-form-item>
            </el-form>
          </el-scrollbar>
        </pane>
        <pane>
          <el-card class="abbreviation-wapper" shadow="never" v-loading="poolLoading">
            <template #header>
              <slot name="header">
                <span class="dis-checkbox">
                  <el-checkbox v-model="checkAll" size="small" @change="onAllSelectChange"></el-checkbox>
                </span>
                <span class="list-total-tip">{{ $t('text_result_total', { total: chatListData.total }) }}</span>
                <!-- 排序 -->
                <el-form size="small" :inline="true" style="float: right;">
                  <el-form-item style="margin-right: 5px">
                    <div class="sort-box">
                      <el-link icon="CaretTop" :style="{ color: orderBy === 'asc' ? '#4aa181' : '#606266' }" :underline="false" @click="handleSortAsc"></el-link>
                      <el-link icon="CaretBottom" :style="{ color: orderBy === 'desc' ? '#4aa181' : '#606266' }" :underline="false" @click="handleSortDesc"></el-link>
                    </div>
                  </el-form-item>
                  <el-form-item>
                    <el-select v-model="poolSortType" :placeholder="$t('place_select')" :style="'width: 100px;'">
                      <el-option v-for="(v, index) in enumList.DcPoolSort" :key="index" :label="$t(v.name)"
                        :value="v.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
              </slot>
            </template>
            <div class="dis-flex">
              <el-scrollbar class="chat-list">
                <div class="item-waper" v-for="(v, k) in chatListData.list" v-if="chatListData.list.length > 0">
                  <el-descriptions @click.stop="activeChatHandle(v)" :key="k" :column="2" size="small" border :id="'item' + v.dsc_user_id + v.dm_channel">
                    <template #title>
                      <el-button link v-if="!v.showMarkInput" @click.stop="handleClickMark(k)" :class="[v.note ? 'mark-color' : '']">{{ v.note.trim() || $t('text_click_add_mark') }}</el-button>
                      <div class="dis-mark" v-if="v.showMarkInput" @click.stop>
                        <el-input v-model="v.note" maxlength="50" show-word-limit type="text" :placeholder="$t('text_please_mark_content')" @blur="handleInputBlur(k)" />
                      </div>
                      <div class="dc-list-title">
                        <span class="dis-checkbox" @click.stop>
                          <el-checkbox style="float: left;" v-model="v.checked" @change="handleCheckItemChange(v)" />
                        </span>
                        <span class="mr-36">{{ $t('text_dc_nick') }}：{{ v.user_name }}</span>
                        <span class="mr-36">UID：{{ v.uid ? v.uid : '-' }}</span>
                        <span class="mr-36">{{ $t('text_server') }}：{{ v.sid ? v.sid : '-' }}</span>
                        <span>{{ $t('text_bots') }}：{{ v.bot_show ? v.bot_show : '-' }}</span>
                      </div>
                    </template>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_dc_pw') }} </div>
                      </template>
                      {{ v.dsc_user_id }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_game') }} </div>
                      </template>
                      {{ v.project ? v.project : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_total_pay') }} </div>
                      </template>
                      {{ v.total_pay ? v.total_pay : 0 }}$
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_thirty_day_pay') }} </div>
                      </template>
                      {{ v.pay_last_thirty_days ? v.pay_last_thirty_days : 0 }}$
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div>
                          <span>{{ $t('text_status') }}</span>
                          <el-button v-has="'discord:refresh'" type="primary" link icon="Refresh" @click.stop="handleRefresh(v)"></el-button>
                        </div>
                      </template>
                      <span :style="{ color: colorsMap[v.status], 'font-weight': 'bold' }">
                        {{ enumList.DiscordServiceReplyStatus.find((item: Record<string, string | number>) => item.value === v.status)?.name || '-' }}
                      </span>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ v.status === 2 ? $t('text_last_replay_times') : $t('text_pending_time') }} </div>
                      </template>
                      <span v-if="v.status === 2">{{ v.last_reply_time ? v.last_reply_time : '-' }}</span>
                      <span v-else>{{ v.waiting_time ? v.waiting_time : '-' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_vip_level') }} </div>
                      </template>
                      {{ v.vip_level ? v.vip_level : 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_last_last_login_time') }} </div>
                      </template>
                      {{ v.last_login ? v.last_login : '-' }}
                    </el-descriptions-item>
                  </el-descriptions>
                  <div class="coupled-data" v-if="v.ticket_create_count > 0">
                    该玩家7日内提交过{{ v.ticket_create_count }}个工单, 最近一条提交时间：{{ v.last_ticket_create_time }}（工单ID：<span class="link-btn" @click="jumpHandle(v.ticket_id)">{{ v.ticket_id }}</span>）
                  </div>
                </div>
                <el-empty v-else :description="$t('info_no_data')" />
              </el-scrollbar>
            </div>
            <template #footer>
              <el-pagination class="pagination" v-model:current-page="chatListData.page" :small="true"
                :page-sizes="[20, 50, 150, 200, 500, 1000]" v-model:page-size="chatListData.pageSize"
                :total="chatListData.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </template>
          </el-card>
        </pane>
      </splitpanes>
    </pane>
    <!-- 右侧内容区域 -->
    <pane size="40" style="min-width: 410px;">
      <div class="o-right" v-if="activechartInfo.dm_channel">
        <el-descriptions class="play-baseinfo" direction="horizontal" size="small" :column="3" border>
          <template #title>
            <div class="dc-list-title">
              <span>{{ $t('text_base_info') }}</span>
              <span style="float: right">{{ $t('text_maintainer') }}：{{ activechartInfo.processor ?
                activechartInfo.processor : $t('text_none') }}</span>
            </div>
          </template>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_game') }} </span>
            </template>
            {{ activechartInfo.project ? activechartInfo.project : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_player_nickname') }} </span>
            </template>
            {{ activechartInfo.user_name ? activechartInfo.user_name : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_player_dcid') }} </span>
            </template>
            {{ activechartInfo.dsc_user_id ? activechartInfo.dsc_user_id : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> UID </span>
            </template>
            {{ activechartInfo.uid ? activechartInfo.uid : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> Fpid </span>
            </template>
            {{ activechartInfo.fpid ? activechartInfo.fpid : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_server') }} </span>
            </template>
            {{ activechartInfo.sid ? activechartInfo.sid : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_game_names') }} </span>
            </template>
            {{ activechartInfo.player_nick ? activechartInfo.player_nick : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_lang') }} </span>
            </template>
            {{ activechartInfo.lang ? activechartInfo.lang : '-' }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="chat-tab-box">
          <el-tabs v-model="activeTab" style="height:100%" v-loading="tabLoading" @tab-change="changeTabHandle">
            <el-tab-pane :label="$t('text_online_chat')" :name="1">
              <!-- discord模块 -->
              <opsDcChat :partner-info="activechartInfo" v-if="activechartInfo && activechartInfo.dm_channel"
                :key="activechartInfo.dsc_user_id + activechartInfo.dm_channel" />
            </el-tab-pane>
            <el-tab-pane :label="$t('text_user_portrait')" :name="2" v-loading="portraitLoading">
              <el-scrollbar style="box-sizing: border-box; padding: 0px 12px 0px;">
                <!-- 标签信息 -->
                <div class="common-title-box">
                  <span class="common-title">{{ $t('text_tag_info') }}</span>
                  <el-button size="small" plain type="primary" @click="handleTagToggle">{{ $t('btn_edit') }}</el-button>
                </div>
                <div class="tag-box">
                  <div class="flex" v-if="tagLists.length > 0">
                    <el-tag v-for="(item, index) in tagLists" :key="index" effect="plain" size="small" style="margin: 0 0 5px 10px">
                      {{ item.tag_desc }}
                    </el-tag>
                  </div>
                  <div class="no-data-box" v-else><span class="no-data">暂无标签</span></div>
                </div>
                <el-divider border-style="dashed" class="line" />
                <!-- 基础属性 -->
                <div class="common-title-box">
                  <span class="common-title">{{ $t('text_base_attribute') }}</span>
                  <el-button size="small" type="primary" plain @click="handleBaseToggle">{{ baseBtnText }}</el-button>
                </div>
                <div class="base-info">
                  <el-form class="base-info-form" label-position="top" size="small" :model="baseForm" ref="formRef"
                    :inline="true">
                    <!-- 性别 -->
                    <el-form-item :label="$t('text_gender')">
                      <opsSelect v-model="baseForm.gender" :placeholder="$t('place_select')" :disabled="!isEditMode"
                        clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerGender" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                    <!-- 生日 -->
                    <el-form-item :label="$t('text_birthday')">
                        <el-date-picker
                          v-model="baseForm.birthday"
                          type="date"
                          :placeholder="$t('text_birthday')"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                          :disabled="!isEditMode"
                          style="width: 200px"
                        />
                    </el-form-item>
                    <!-- 职业 -->
                    <el-form-item :label="$t('text_career')">
                      <el-input v-model="baseForm.career" clearable :placeholder="$t('know_m_rich_placeholder')"
                        :disabled="!isEditMode" style="width: 200px" />
                    </el-form-item>
                    <!-- 教育程度 -->
                    <el-form-item :label="$t('text_education_level')">
                      <opsSelect v-model="baseForm.education_level" :placeholder="$t('place_select')"
                        :disabled="!isEditMode" clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerEducationLevel" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                    <!-- 婚姻状况 -->
                    <el-form-item :label="$t('text_marital_status')">
                      <opsSelect v-model="baseForm.married_state" :placeholder="$t('place_select')"
                        :disabled="!isEditMode" clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerMarriageState" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                    <!-- 生育状况 -->
                    <el-form-item :label="$t('text_ertility_status')">
                      <opsSelect v-model="baseForm.fertility_state" :placeholder="$t('place_select')"
                        :disabled="!isEditMode" clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerFertilityState" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                  </el-form>
                </div>
                <el-divider border-style="dashed" class="line" />
                <!-- 备注信息 -->
                <div class="common-title-box">
                  <span class="common-title">{{ $t('text_remark_info') }}</span>
                  <el-button size="small" type="primary" plain @click="handleRemarkToggle">{{ remarkBtnText
                    }}</el-button>
                </div>
                <div class="remark-box">
                  <el-input type="textarea" :rows="4" :disabled="!isRemarkEditMode" placeholder="请输入内容"
                    v-model="baseForm.remark">
                  </el-input>
                </div>
              </el-scrollbar>
            </el-tab-pane>
            <!-- 工单历史 -->
            <el-tab-pane :label="$t('text_history_ticket')" :name="3">
              <el-scrollbar style="height:100%; padding:0px 15px">
                <el-descriptions v-for="(v, k) in baseForm.ticket_info" v-if="baseForm.ticket_info && baseForm.ticket_info.length > 0" @click.stop="historyTicketHandle(v)"
                  :key="k" :title="`${$t('text_ticket')}ID：${v.ticket_id}`" :column="2" size="small" border>
                  <el-descriptions-item label-class-name="my-label">
                    <template #label>
                      <div> {{ $t('text_game') }} </div>
                    </template>
                    {{ v.project ? v.project : '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label-class-name="my-label">
                    <template #label>
                      <div> {{ $t('text_acc_amount') }} </div>
                    </template>
                    {{ v.recharge }}$
                  </el-descriptions-item>
                  <el-descriptions-item label-class-name="my-label">
                    <template #label>
                      <div> {{ $t('text_ticket_status') }} </div>
                    </template>
                    {{ enumList.ConversionNode.find((item: Record<string, string|number>) => item.value ===
                      v.status)?.name || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label-class-name="my-label">
                    <template #label>
                      <div> {{ $t('text_pending_time') }} </div>
                    </template>
                    {{ v.waiting_time }}
                  </el-descriptions-item>
                  <el-descriptions-item label-class-name="my-label" :span="2">
                    <template #label>
                      <div> {{ $t('text_content') }} </div>
                    </template>
                    {{ v.detail ? v.detail : $t('text_none')}}
                  </el-descriptions-item>
                </el-descriptions>
                <el-empty v-else :description="$t('info_no_data')" />
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <el-empty v-else :description="$t('info_no_data')" />
    </pane>
    <add-tag v-if="tagEditVisible" v-model:visible="tagEditVisible" :edit-data="tagData" :params-data="paramsData"
      @success="searchHandle"></add-tag>
    <batch-message v-if="showBatchBox" v-model:visible="showBatchBox" :ticket-ids="uniqueArr" :params-data="messageData" @success="search"></batch-message>
    <add-tab v-if="tabVisible" v-model:visible="tabVisible" :params-data="tabData" @success="getTabList"></add-tab>
    <patch-tag v-if="patchTagVisible" v-model:visible="patchTagVisible" :user-ids="checkUserIdList" :params-data="projectData" @success="search"></patch-tag>
    <!-- 批量删除标签组件 -->
    <batch-deltag v-if="batchDelTagVisible" v-model:visible="batchDelTagVisible" :params-data="projectData" :batch-ids="checkUserIdList" @success="search"/>
  </splitpanes>
</template>

<script lang="ts">
import type { FormInstance, CheckboxValueType } from 'element-plus'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import { useEnumStore, useUserInfoStore, useAppStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import { getAcceptorList } from '@/api/assignConfig'
import { playerDCPoll, getStats, getPortrait, discordDown, addPortrait, addMark, fetchTabLists, deleteTab, discordTagList, robotList, discordReplyStatus, getTagList, getTabCount, updateTabSettingOrder } from '@/api/chatting'
import opsDcChat from './components/opsDcChat.vue'
import addTag from './components/addTag.vue'
import batchMessage from './components/batchMessage.vue'
import batchDeltag from './components/batchDelDCTag.vue'
import addTab from './components/addTab.vue'
import patchTag from './components/pathAddTag.vue'
import Sortable from 'sortablejs'
import DragIcon from '@/components/DragIcon.vue'
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>)
export default defineComponent({
  name: 'Discord',
  components: { Splitpanes, Pane, ElMessageBox, opsDcChat, addTag, batchMessage, addTab, patchTag, batchDeltag }
})
interface SearchForm {
  project: string[]
  replied_at: Array<string>
  last_login: Array<string>
  status: number[]
  pay_all_prev?: number
  pay_all_next?: number
  pay_prev?: number
  pay_next?: number
  vip_state?: number
  tag: string[]
  processor: string[]
  dsc_user_nickname: string
  bot_ids: string[]
  user_content: string
  user_detail_remark: string
  dsc_user_id: string
  uids?: number
  fpid: string
  sid: string
  bot_id: string
  note?: string
  checked?: boolean
  birthday?: string
  language?: string[]
  dm_channel?: string
  pay_last_thirty_days?: number[] | undefined
  pay_all?: number[] | undefined
  page?: number
  page_size?: number,
  reply_type?: number | undefined,
  last_reply_service: string[],
  tag_type: number | null,
  tags: number[]
  tab_name?: string
  id?: number
  public?: number
}
interface LabelObject {
  tag_id: number
  tag_name: string
}
interface BaseForm {
  tag: number[]
  id: number
  dsc_user_id: string
  fpid: string
  game_project: string
  tags?: LabelObject[]
  gender?: number
  birthday: string
  career: string
  education_level?: number
  married_state?: number
  fertility_state?: number
  remark: string
}
type SearchFormKey = keyof SearchForm
type SearchFormValue = SearchForm[keyof SearchForm]
interface shortcutButtonT {
  id: number
  lable: string
  shortcut: Record<string, unknown>
}
</script>
<script setup lang="ts">
const router = useRouter()
const { t: $t } = useI18n()
const csList = ref<Record<string, string>[]>([])
const isEditMode = ref<Boolean>(false)
const isRemarkEditMode = ref<Boolean>(false)
const portraitLoading = ref(false)
const tagEditVisible = ref(false)
const tabVisible = ref(false)
const patchTagVisible = ref(false)
const tabsLoading = ref(false)
const tagData = ref({})
const paramsData = ref({})
const messageData = ref({})
const tabData = ref<SearchForm>({} as SearchForm)
const projectData = ref({})
const orderBy = ref('')
const poolSortType = ref()
const baseBtnText = ref<String>($t('btn_edit'))
const remarkBtnText = ref<String>($t('btn_edit'))
const tagLists = ref<Array<{id: number, tag_name: string}>>([])
const isTimerActive = ref(true) // 表示定时任务应该运行
const tabList = ref<any[]>([])
const openIndex = ref<string[]>([])
const discordTagData = ref<Array<{id: number, tag_name: string}>>([])
const checkAll = ref(false)
const checkDataList = ref<number[]>([])
const checkUserIdList = ref<string[]>([])
const checkProject = ref<string[]>([])
const checkBotId = ref<string[]>([])


const cascaderProps = { multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum)
const progState = ref<boolean>(false)
watch(progressNum, (n) => {
  progState.value = (n < 100 && n > -1) ? true : false
})
const colorsMap = {
  1: '#E6A23C', // 待处理-黄色
}
const getCsList = async () => {
  const res = await getAcceptorList({})
  csList.value = res
}
getCsList()
// 获取固定tab列表
const getTabList = async () => {
  tabsLoading.value = true
  tabList.value = []
  try {
    const tabLists = await fetchTabLists({})
    const tabCounts = await getTabCount({})
    if (tabLists.data.length === 0) {
      tabsLoading.value = false
      return
    }
    tabLists.data.forEach((item1: any) => {
      const projectName = item1.project
      const newTabList = [] as any
      openIndex.value.push(projectName) // 默认展开所有tab菜单项
      item1.tab.forEach((tab1: any) => {
        // 在tabCounts中寻找对应的项目
        const matchingProject = tabCounts.detail.find((item2: any) => item2.project === projectName)
        if (matchingProject) {
          // 找到匹配的项目后，查找对应的tab_name
          const matchingTab = matchingProject.tab.find((tab2: any) => tab2.tab_name === tab1.tab_name)
          if (matchingTab) {
            // 如果匹配成功，输出对应的值
            newTabList.push({
              ...tab1,
              count: matchingTab.count,
            })
          }
        }
      })
      // 组成新的数据结构
      tabList.value.push({
        project: projectName,
        tabs: newTabList,
      })
      tabsLoading.value = false
    })
  } catch (error) {
    console.log(error)
    tabsLoading.value = false
  }
}
getTabList()
// 获取discord标签库
const getDiscordTag = () => {
  discordTagList({status: 1}).then((res: any) => {
    if (res && Array.isArray(res.data)) {
      discordTagData.value = res.data
    }
  })
}
getDiscordTag()
const searchFormInitData = {
  project: [],
  replied_at: [],
  last_login: [],
  tag: [],
  status: [],
  processor: [],
  user_detail_remark: '',
  user_content: '',
  dsc_user_nickname: '',
  bot_ids: [],
  dsc_user_id: '',
  fpid: '',
  sid: '',
  bot_id: '',
  reply_type: undefined,
  last_reply_service: [],
  tag_type: null,
  tags: [],
}
// 快捷查询按钮
const ovButtonList = [
  {
    id: 1,
    lable: 'text_all_vip_account',
    shortcut: searchFormInitData,
    prop: 'discord_user_count'
  },
  {
    id: 2,
    lable: 'text_pending_reply_message_account',
    shortcut: {
      status: [1]
    },
    prop: 'wait_reply_accounts'
  },
  {
    id: 3,
    lable: 'text_mine_pending_reply_account',
    shortcut: {
      status: [1, 2],
      reply_type: 1,
      last_reply_service: 'CurrentUser'
    },
    prop: 'mine_wait_reply_accounts'
  }
]
const overviewData: Record<string, number> = reactive({
  discord_user_count: 0,
  wait_reply_accounts: 0,
  mine_wait_reply_accounts: 0
})
const tagOpts = ref<any[]>([])
const changeGameHandle = (val: string[]) => {
  searchForm.value.tags = []
  tagOpts.value = []
  if (val.length === 1 && val[0]) {
    getTagList({ project_name: val[0] ? val[0] : '', lib_type: 2 }).then((res: any) => {
      if (res && res.data) {
        tagOpts.value = res.data
        tagOpts.value.unshift({tag_name: 'all'})
      }
    })
  }
}
// 标签全选功能
// 递归获取 所有列最后一层 的tag_id
const extractLastLayerTagIds = (items: any[], tagIds: number[] = []): number[] => {
  items.forEach(item => {
    if (item.children && item.children.length > 0) {
      extractLastLayerTagIds(item.children, tagIds)
    } else if (item.tag_id) {
      tagIds.push(item.tag_id)
    }
  })
  return tagIds
}
const tagsAllSelected = ref<boolean>(false)
// 全选/取消全选
const tagsHandleSelectAll = () => {
  const allTagIds: number[] = extractLastLayerTagIds(tagOpts.value)
  if (searchForm.value.tags?.length < allTagIds.length) {
    searchForm.value.tags = []
    searchForm.value.tags = allTagIds
  } else {
    searchForm.value.tags = []
  }
}
// 标签选择时 判断是否全选
const handleTagChange = (val: number[]) => {
  const allTagIds: number[] = extractLastLayerTagIds(tagOpts.value)
  if (val.length === allTagIds.length) {
    tagsAllSelected.value = true
  } else {
    tagsAllSelected.value = false
  }
}
const handleTagType = () => {
  searchForm.value.tags = []
}
// 查询聊天列表模块
const chatListData = reactive({
  list: [] as Record<string, unknown>[], // 表格数据
  total: 0, // 总数
  page: 1, // 当前页
  pageSize: 20, // 每页条数
})

const poolLoading = ref(false)
const search = async () => {
  poolLoading.value = true
  const { dsc_user_nickname, uids, pay_prev, pay_next, pay_all_prev, pay_all_next} = searchForm.value
  const params = Object.assign({}, searchForm.value, {
    dsc_user_nickname: [dsc_user_nickname].filter(Boolean),
    // uid: Number(uid) === 0 ? null : Number(uid),
    pay_last_thirty_days:[pay_prev, pay_next].filter(item => item !== undefined),
    pay_all: [pay_all_prev, pay_all_next].filter(item => item !== undefined),
    order: orderBy.value,
    sort_by: poolSortType.value,
    page: chatListData.page,
    page_size: chatListData.pageSize
  })
  await playerDCPoll(params).then((res: any) => {
    chatListData.list = res.data
    chatListData.total = res.total
  }).finally(() => {
    poolLoading.value = false
    refreshTable()
    chatListData.list.forEach((item: any) => {
      item.checked = false
      checkAll.value = false
      item.showMarkInput = false
    })
    checkDataList.value = []
    checkUserIdList.value = []
    checkProject.value = []
    checkBotId.value = []
  })
}
const handleSizeChange = (val: number) => {
  chatListData.page = 1
  chatListData.pageSize = val
  search()
}
const handleCurrentChange = (val: number) => {
  chatListData.page = val
  search()
}

// 点击具体聊天对象
const activechartInfo = ref<Record<string, any>>({})
const activeChatHandle = (v: Record<string, unknown>) => {
  activechartInfo.value = {}
  activechartInfo.value = v
  v.showMarkInput = false
  tagLists.value = []
  getPlayerInfo()
}
// 聊天列表list自动滚动active item功能
const refreshTable = () => {
  if (activechartInfo.value) {
    const element = document.querySelector(`#item${activechartInfo.value.dsc_user_id}${activechartInfo.value.dm_channel}`)
    if (element) {
      element.scrollIntoView()
    }
  }
}
const gameList = computed(() => useEnumStore().gameList)
const enumList = computed(() => useEnumStore().enumList)
const langList = computed(() => useEnumStore().LangsList)
const searchFormRef = ref<FormInstance>()
const searchForm = ref<SearchForm>({ ...searchFormInitData })
// DC机器人列表（根据游戏变化）
const botsList = ref<Record<string, string>>({})
watch(() => searchForm.value.project, (n) => {
  searchForm.value.bot_ids = []
  if (n.length === 0) {
    botsList.value = {}
    return
  }
  robotList({projects: n}).then((res: any) => {
    botsList.value = res
  })
}, { immediate: true })
const baseForm = ref<BaseForm>({} as BaseForm)
const activeShortcutId = ref(0)
const activeTabId = ref<number>(0)
const shortcutHandle = (v: shortcutButtonT) => {
  activeTabId.value = 0
  activeShortcutId.value = v.id
  searchForm.value = { ...searchFormInitData }
  Object.keys(v.shortcut).forEach((key) => {
    if (key in searchForm.value) {
      if (key === 'last_reply_service' && v.shortcut[key] === 'CurrentUser') {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = Array.of(userInfo.value.username) as SearchFormValue
      } else {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = v.shortcut[key] as SearchFormValue
      }
    }
  })
  search()
}
// 自动加载第一个按钮查询数据
shortcutHandle(ovButtonList[0])
// 重置按钮
const reset = () => {
  searchFormRef.value?.resetFields()
  shortcutHandle(ovButtonList[0])
  poolSortType.value = null
  orderBy.value = ''
}
// 获取概览数据
const getOverview = async() => {
  try {
    const res = await getStats({})
    Object.keys(overviewData).forEach((key) => {
      overviewData[key] = res[key]
    })
  } catch (error) {
    console.log(error)
  }
}
getOverview()
// 下载
const donwLoad = () => {
  const { dsc_user_nickname, uids, pay_prev, pay_next, pay_all_prev, pay_all_next} = searchForm.value
  const params = Object.assign({}, searchForm.value, {
    dsc_user_nickname: [dsc_user_nickname].filter(Boolean),
    // uid: Number(uid) === 0 ? null : Number(uid),
    pay_last_thirty_days:[pay_prev, pay_next].filter(Boolean).filter(item => item !== undefined),
    pay_all: [pay_all_prev, pay_all_next].filter(Boolean).filter(item => item !== undefined),
    page: 1,
    page_size: 20
  })
  discordDown(params)
}
// 获取玩家画像
const getPlayerInfo =() => {
  portraitLoading.value = true
  tagLists.value = []
  getPortrait({
    dsc_user_id: activechartInfo.value.dsc_user_id,
    game_project: activechartInfo.value.project
  }).then((res: any) => {
    if (res.label) {
      tagLists.value = res.new_label
    }
    baseForm.value = { ...res }
  }).catch((error: Error) => {
    console.error('Failed to get player info:', error)
  }).finally(() => {
    portraitLoading.value = false
  })
}
const handleTagToggle = () => {
  tagEditVisible.value = true
  paramsData.value = {
    dsc_user_id: activechartInfo.value.dsc_user_id,
    game_project: activechartInfo.value.project,
  }
  tagData.value = {
    ...baseForm.value,
    game_project: activechartInfo.value.project,
  }
}
const handleBaseToggle = () => {
  baseBtnText.value = isEditMode.value ? $t('btn_edit') : $t('text_save')
  isEditMode.value = !isEditMode.value
  if(!isEditMode.value){
    handleSaveAndRefresh()
  }
}
const handleRemarkToggle = () => {
  remarkBtnText.value = isRemarkEditMode.value ? $t('btn_edit') : $t('text_save')
  isRemarkEditMode.value = !isRemarkEditMode.value
  if(!isRemarkEditMode.value) {
    handleSaveAndRefresh()
  }
}
// 画像保存
const handleSaveAndRefresh = () => {
  let params = Object.assign({}, baseForm.value, {
    dsc_user_id: activechartInfo.value.dsc_user_id,
    game_project: activechartInfo.value.project,
    id: baseForm.value.id,
    label: ((tagLists || []).map(item => item.tag_id)).join(',')
  })
  addPortrait(params).then(() => {
    ElMessage.success($t('text_success'))
    getPlayerInfo()
  })
}
const searchHandle = () => {
  getPlayerInfo()
}
// 30累计付费金额左侧范围判断
const getPayVal = () => {
  searchForm.value.pay_prev = searchForm.value.pay_prev || 0
  if (searchForm.value.pay_next === undefined) {
    searchForm.value.pay_next = 0
  }
  if (searchForm.value.pay_prev === 0 && searchForm.value.pay_next === 0) {
    searchForm.value.pay_prev = undefined
    searchForm.value.pay_next = undefined
  }
}
// 30累计付费金额右侧范围判断
const getPayNextVal = () => {
  searchForm.value.pay_next = searchForm.value.pay_next || 0
  if (searchForm.value.pay_next && !searchForm.value.pay_prev) {
    searchForm.value.pay_prev = 0
  }
  if (searchForm.value.pay_prev === 0 && searchForm.value.pay_next === 0) {
    searchForm.value.pay_prev = undefined
    searchForm.value.pay_next = undefined
  }
}
// 累计付费金额左侧范围判断
const getPayAllVal = () => {
  searchForm.value.pay_all_prev = searchForm.value.pay_all_prev || 0
  if (searchForm.value.pay_all_next === undefined) {
    searchForm.value.pay_all_next = 0
  }
  if (searchForm.value.pay_all_prev === 0 && searchForm.value.pay_all_next === 0) {
    searchForm.value.pay_all_prev = undefined
    searchForm.value.pay_all_next = undefined
  }
}// 累计付费金额右侧范围判断
const getPayAllNextVal = () => {
  searchForm.value.pay_all_next = searchForm.value.pay_all_next || 0
  if (searchForm.value.pay_all_next && !searchForm.value.pay_all_prev) {
    searchForm.value.pay_all_prev = 0
  }
  if (searchForm.value.pay_all_prev === 0 && searchForm.value.pay_all_next === 0) {
    searchForm.value.pay_all_prev = undefined
    searchForm.value.pay_all_next = undefined
  }
}

// 维护人全选
const checkProcessor = ref<boolean>(false)
const indeterminateProcessor = ref<boolean>(false)
watch(() => searchForm.value.processor, (val) => {
  if (val?.length === 0) {
    checkProcessor.value = false
    indeterminateProcessor.value = false
  } else if (val?.length === csList.value.length) {
    checkProcessor.value = true
    indeterminateProcessor.value = false
  } else {
    indeterminateProcessor.value = true
  }
})
const processorCheckAll = (val: CheckboxValueType) => {
  indeterminateProcessor.value = false
  if (val) {
    searchForm.value.processor = csList.value.map((_) => _.account)
  } else {
    searchForm.value.processor = []
  }
}

// 最近处理人全选
const checkLast = ref<boolean>(false)
const indeterminateLast = ref<boolean>(false)
watch(() => searchForm.value.last_reply_service, (val) => {
  if (val?.length === 0) {
    checkLast.value = false
    indeterminateLast.value = false
  } else if (val?.length === csList.value.length) {
    checkLast.value = true
    indeterminateLast.value = false
  } else {
    indeterminateLast.value = true
  }
})
const lastCheckAll = (val: CheckboxValueType) => {
  indeterminateLast.value = false
  if (val) {
    searchForm.value.last_reply_service = csList.value.map((_) => _.account)
  } else {
    searchForm.value.last_reply_service = []
  }
}

// DC机器人全选
const checkBotids = ref<boolean>(false)
const indeterminateBotids = ref<boolean>(false)
watch(() => searchForm.value.bot_ids, (val) => {
  if (val?.length === 0) {
    checkBotids.value = false
    indeterminateBotids.value = false
  } else if (val?.length === Object.keys(botsList.value).length) {
    checkBotids.value = true
    indeterminateBotids.value = false
  } else {
    indeterminateBotids.value = true
  }
})
const botidsCheckAll = (val: CheckboxValueType) => {
  indeterminateBotids.value = false
  if (val) {
    searchForm.value.bot_ids = Object.keys(botsList.value)
  } else {
    searchForm.value.bot_ids = []
  }
}

// 语言全选
const checkLang = ref<boolean>(false)
const indeterminateLang = ref<boolean>(false)
watch(() => searchForm.value.language, (val) => {
  if (val?.length === 0) {
    checkLang.value = false
    indeterminateLang.value = false
  } else if (val?.length === langList.value.length) {
    checkLang.value = true
    indeterminateLang.value = false
  } else {
    indeterminateLang.value = true
  }
})
const langCheckAll = (val: CheckboxValueType) => {
  indeterminateLang.value = false
  if (val) {
    searchForm.value.language = langList.value.map((_) => _.code)
  } else {
    searchForm.value.language = []
  }
}

const uniqueArr = ref<number[]>([])
// 全选
const onAllSelectChange = () => {
  const clearSelection = () => {
    checkUserIdList.value = []
    checkDataList.value = []
    checkProject.value = []
    checkBotId.value = []
  }

  if (checkAll.value) {
    clearSelection()
    chatListData.list.forEach((item: any) => {
      item.checked = true
      checkUserIdList.value.push(item.dsc_user_id)
      checkDataList.value.push(item.uid)
      checkProject.value.push(item.project)
      checkBotId.value.push(item.bot_id)
    })
    uniqueArr.value = [...new Set(checkDataList.value)]
  } else {
    chatListData.list.forEach((item: any) => {
      item.checked = false
    })
    clearSelection()
  }
}
// 单选
const handleCheckItemChange = (val: { checked: boolean, dsc_user_id: string, uid: number, bot_id: string, project: string }) => {
  const updateList = (list: any[], value: any, action: 'add' | 'remove') => {
    const index = list.indexOf(value)
    if (action === 'add') {
      list.push(value)
    } else if (action === 'remove' && index > -1) {
      list.splice(index, 1)
    }
  }
  if (val.checked) {
    updateList(checkDataList.value, val.uid, 'add')
    updateList(checkUserIdList.value, val.dsc_user_id, 'add')
    updateList(checkProject.value, val.project, 'add')
    updateList(checkBotId.value, val.bot_id, 'add')
    uniqueArr.value = [...new Set(checkDataList.value)]
  } else {
    updateList(checkDataList.value, val.uid, 'remove')
    updateList(checkUserIdList.value, val.dsc_user_id, 'remove')
    updateList(checkProject.value, val.project, 'remove')
    updateList(checkBotId.value, val.bot_id, 'remove')
    uniqueArr.value = [...new Set(checkDataList.value)]
  }
}
// 批量私信
const showBatchBox = ref(false)
const handleAllCheckData = () => {
  const projectList = [...new Set(checkProject.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_path_message_single_game'))
    return
  }
  showBatchBox.value = true
  messageData.value = {
    bot_id: [...new Set(checkBotId.value)].join(','),
    project: projectList[0],
  }
}
// 中间内容排序
const handleSortAsc = () => {
  orderBy.value = 'asc'
}
const handleSortDesc = () => {
  orderBy.value = 'desc'
}
// 点击添加备注
const handleClickMark = (index: number) => {
  isTimerActive.value = false
  chatListData.list[index].showMarkInput = true
}
// 失去焦点关闭文本框，并发送请求
const handleInputBlur = (index: number) => {
  const item = chatListData.list[index]
  if (!item) {
    // 如果索引无效，则不执行任何操作
    return
  }
  // if(!item.note) {
  //   item.showMarkInput = false
  //   return
  // }
  item.showMarkInput = false
  addMark({
    channel_id: item.dm_channel,
    note: item.note
  }).then(() => {
    ElMessage.success($t('text_add_mark_success'))
    isTimerActive.value = true
  }).catch((error: any) => {
    // 处理可能的错误
    console.log('Failed to add note:', error)
    isTimerActive.value = true
  })
}
// 修正客服对玩家消息的回复状态
const handleRefresh = (val: { dsc_user_id: string, dm_channel: string, status: number }) => {
  discordReplyStatus({
    channel_id: val.dm_channel,
    dsc_user_id: val.dsc_user_id,
    old_reply_status: val.status,
    new_reply_status: val.status === 1 ? 2 : 1
  }).then(() => {
    ElMessage.success($t('text_refresh_success'))
    search()
  })
}
// 固定tab
const handleAddTabData = () => {
  const { project, dsc_user_nickname, pay_prev, pay_next, pay_all_prev, pay_all_next} = searchForm.value
  if(project.length > 1) {
    ElMessage.error($t('text_only_supports_creating_single_game'))
    return
  }
  if (pay_prev !== undefined && pay_next !== undefined && pay_prev > pay_next) {
    ElMessage.error($t('text_thirty_pay_left_cant_over_right'))
    return
  }
  if (pay_all_prev !== undefined && pay_all_next !== undefined && pay_all_prev > pay_all_next) {
    ElMessage.error($t('text_total_pay_left_cant_over_right'))
    return
  }
  tabVisible.value = true
  const params = Object.assign({}, searchForm.value, {
    dsc_user_nickname: [dsc_user_nickname].filter(Boolean),
    // uid: Number(uid) === 0 ? null : Number(uid),
    pay_last_thirty_days:[pay_prev, pay_next].filter(item => item !== undefined),
    pay_all: [pay_all_prev, pay_all_next].filter(item => item !== undefined),
    page: chatListData.page,
    page_size: chatListData.pageSize
  })
  tabData.value = params
}
// 固定tab-点击事件
const handleClickTab = (v: {id: number, detail: SearchForm }) => {
  activeShortcutId.value = 0
  activeTabId.value = v.id
  if (v.detail) {
    const { dsc_user_nickname, uids, pay_last_thirty_days, pay_all } = v.detail
    let params = { ...v.detail }
    // 检查 pay_last_thirty_days 和 pay_all 是否已定义且不是 undefined，且长度大于0
    if (Array.isArray(pay_last_thirty_days) && pay_last_thirty_days.length > 0) {
      params.pay_prev = pay_last_thirty_days[0]
      if (pay_last_thirty_days.length > 1) {
        params.pay_next = pay_last_thirty_days[1]
      }
    }
    if (Array.isArray(pay_all) && pay_all.length > 0) {
      params.pay_all_prev = pay_all[0]
      if (pay_all.length > 1) {
        params.pay_all_next = pay_all[1]
      }
    }
    // 检查 dsc_user_nickname 是否是数组且不为空
    if (Array.isArray(dsc_user_nickname) && dsc_user_nickname.length > 0) {
      params.dsc_user_nickname = dsc_user_nickname[0]
    }else {
      params.dsc_user_nickname = ''
    }
    if(Number(params.tag_type) === 0) {
      params.tag_type = null
    }
    // 其他属性赋值
    params.page = chatListData.page
    params.page_size = chatListData.pageSize
    searchForm.value = params
  }
  search()
}
// 编辑tab
const handleEditTab = (v: { id: number, tab_name: string, public: number }) => {
  tabVisible.value = true
  tabData.value = {
    ...searchForm.value,
    tab_name: v.tab_name,
    id: v.id,
    public: v.public
  }
}
// 删除tab
const handleDelete = async (v: { id: number, tab_name: string }) => {
  ElMessageBox.confirm(`确定要删除名称为: ( ${v.tab_name} )的Tab吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    tabsLoading.value = true
    deleteTab({ id: v.id }).then(() => {
      ElMessage.success($t('text_del_success'))
      getTabList()
      tabsLoading.value = false
      reset()
    }).catch((error: any) => {
      console.log(error)
      tabsLoading.value = false
    })
  })
}
// 批量打标签
const handlePatchTag = () => {
  const projectList = [...new Set(checkProject.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_patch_tags_only_single_game'))
    return
  }
  patchTagVisible.value = true
  projectData.value = {
    project: projectList.join(',')
  }
}

// 批量删除标签
const batchDelTagVisible = ref(false)
const batchDelTagHandle = () => {
  const projectList = [...new Set(checkProject.value)]
  if(projectList.length > 1) {
    ElMessage.error($t('text_batch_del_tags_only_single_game'))
    return
  }
  batchDelTagVisible.value = true
  projectData.value = {
    project: projectList.join(',')
  }
}

// 判断对象value是否有值
const isObjectNonEmpty = (obj: any): boolean => {
  // 检查对象是否为空或未定义
  if (!obj || typeof obj !== 'object') {
    return false
  }
  // 使用for...in循环遍历对象的所有可枚举属性
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      // 检查值是否不是undefined、null、空字符串""或空数组[]
      if (
        value !== undefined &&
        value !== null &&
        !(typeof value === 'string' && value === '') &&
        !(Array.isArray(value) && value.length === 0)
      ) {
        // 如果找到至少一个非空值，则返回true
        return true
      }
    }
  }
  // 如果没有找到任何非空值，则返回false
  return false
}
const activeTab = ref(1)
const tabLoading = ref(false)
const changeTabHandle = () => {
  // tabLoading.value = true
  // setTimeout(() => {
  //   tabLoading.value = false
  // }, 1000)
}
provide('activeTab', activeTab)

// 耦合信息跳转路由
const jumpHandle = (v: string) => {
  const newTab = router.resolve({
    name: 'Overview',
    query: { ticket_id: v, origin: 'coupler' }
  })
  window.open(newTab.href, '_blank')
}

// 历史工单点击新开页面跳转当前工单
const historyTicketHandle = (v: any) => {
  jumpHandle(v.ticket_id)
}

let timer: any = null
let tabTimer: any = null
onMounted(async () => {
  // 检查query是否有fpid，如果有,说明是工单跳转过来的，需要自动查询并自动选中，展示详情
  const query = useRoute().query
  if (query.fpid) {
    searchForm.value.fpid = query.fpid as string
    await search()
    if (chatListData.list.length > 0) {
      activeChatHandle(chatListData.list[0])
    } else {
      ElMessage.error('数据错误，并未找到对应的聊天')
    }
  }

  if(isTimerActive.value) {
    // 每1分钟刷新一次数据
    timer = setInterval(() => {
      if(isTimerActive.value) {// 确保在每次执行前检查状态
        search()
        getOverview()
      }
    }, 30000)
  }
  // 自定义数据每个5分钟刷新一次
  tabTimer = setInterval(() => {
    getTabList()
  }, 300000)
  document.body.addEventListener('click', () => {
    chatListData.list.map(item => {
      item.showMarkInput = false
    })
  })
  
  // 初始化排序
  nextTick(() => {
    initSortable()
  })

  // 监听tabList变化重新初始化拖拽
  watch(tabList, () => {
    nextTick(() => {
      initSortable()
    })
  }, { deep: true })
})
onUnmounted(() => {
  clearInterval(timer)
  clearInterval(tabTimer)
})

const updateSortable = () => {
  const menuEl = document.querySelectorAll('.project-sortable-tree .el-sub-menu')
  if (!menuEl) return

  let sort = [];
  menuEl.forEach((el) => {
    const project = el.querySelector(".el-sub-menu__title span").innerText
    const tabs = el.querySelectorAll('.el-menu-item')

    let sortTabs = [];
    tabs.forEach((tab) => {
      const tabId = tab.getAttribute("data-id");
      if (tabId) {
        sortTabs.push(Number(tabId));
      }
    })

    sort.push({
      project: project,
      tab: sortTabs
    })
  })

  updateTabSettingOrder({
    sort_setting: JSON.stringify(sort)
  }).then(() => {
    ElMessage.success("排序成功")
  }).catch(() => {
    ElMessage.error("排序失败")
  })
}

const initSortable = () => {
  const menuEl = document.querySelector('.project-sortable-tree')
  if (!menuEl) return

  new Sortable(menuEl, {
    animation: 150,
    handle: '.el-sub-menu__title',
    onEnd: ({ newIndex, oldIndex, to, from, item }) => {
      updateSortable()
    }
  })

  const menuSubEl = document.querySelectorAll('.project-sortable-tree .el-menu--inline')
  menuSubEl.forEach((el) => {
    new Sortable(el, {
      animation: 150,
      handle: '.el-button',
      onEnd: ({ newIndex, oldIndex, to, from, item }) => {
        updateSortable()
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.o-right {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 5px 15px;

  .play-baseinfo {
    width:100%;
    &:deep(.el-descriptions__title) {
      width: 100% !important;
    }
    &:deep(.el-descriptions__header) {
      margin-bottom: 0px;
    }
  }
  .chat-tab-box {
    width: 100%;
    flex-grow: 1;
    overflow: auto;
    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      &:deep(.el-tabs__content) {
        flex: 1;
      }
      .el-tab-pane {
        height: 100%;
        overflow: auto;
        .el-descriptions {
          cursor: pointer;
          width: 100%;
          margin: 0px auto 30px;
          padding: 8px;
          border-radius: 5px;
          background-color: #f8f9fa;
        }
      }
    }
    .common-title-box {
      display: flex;
      justify-content: space-between;
      height: 24px;
      line-height: 24px;
      margin-top: 10px;
      .common-title{
        font-size: 14px;
        font-weight: bold;
      }
    }
    .tag-box{
      max-height: 100px;
      overflow: auto;
      position: relative;
      margin-top: 10px;
      .no-data-box {
        height: 30px;
      }
      .no-data{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        color: #909399;
      }
    }
    .base-info{
      margin-top: 10px;
      padding-left: 10px;
    }
    .remark-box{
      margin-top: 10px;
    }
  }
}
.split-box {
  height: 100%;
  .pane-wapper {
    box-sizing: border-box;
    padding: 10px 5px;
    .hor-line {
      margin: 0 2px;
      font-size: 16px;
      font-weight: bold;
      color: #606266;
    }
    &:deep(.el-input-number) {
      .el-input{
        min-width: 40px;
      }
    }
    &:deep(.el-input-number--small) {
      // width: 50px;
      .el-input-number__increase {
        display: none !important;
      }
      .el-input-number__decrease {
        display: none !important;
      }
    }
  }

  .o-left {
    .el-scrollbar {
      height: calc(100% - 62px);
    }
    &:deep(.el-sub-menu__title) {
      height: 40px;
      line-height: 40px;
      &:hover {
        background-color: transparent;
      }
    }
    &:deep(.el-menu-item) {
      padding: 0;
      &:hover {
        background-color: transparent;
      }
    }
    .delete-btn {
      padding: 5px;
      position: absolute;
      right: -10px;
      visibility: hidden;
    }
    .edit-btn {
      position: absolute;
      right: 20px;
      visibility: hidden;
    }
    &:deep(.el-button) {
      &:hover {
        .delete-btn, .edit-btn {
          visibility: visible !important;
          color: #4aa181;
          background-color: #edf6f2;
          transition: background-color 0.2s
        }
      }
    }
    .activeBtn {
      color: var(--el-button-hover-text-color);
      border-color: var(--el-button-hover-border-color);
      background-color: var(--el-button-hover-bg-color);
      outline: none;
    }

    .el-button {
      display: block;
      margin: 2px auto 20px;
      width: 90%;
      overflow: hidden;
    }

    .title {
      font-size: 18px;
      height: 40px;
      line-height: 40px;
      font-weight: bold;
      text-align: center;
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      background-image: linear-gradient(to right, rgba(39, 177, 236, 0.8), rgba(74, 161, 129, 0.8));
      box-shadow: 0px 2px 7px -3px rgba(0, 0, 0, 0.6);
      margin-bottom: 20px;

      .text {
        margin-left: 10px;
      }
    }
  }
}
.abbreviation-wapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .dis-checkbox {
    float: left;
    margin-right: 5px;
  }
  .list-total-tip {
    float: left;
    margin-top: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-regular);
  }
  &:deep(.my-label) {
    width: 120px;
    // text-align: right;
  }
  &:deep(.right-label) {
    // width: 120px;
    text-align: right;
  }
  &:deep(.el-form--inline .el-form-item) {
    margin-bottom: 10px;
    margin-right: 0px;
  }
  &:deep(.el-card__header, .el-card__footer) {
    padding: 10px 10px 0px;
  }

  &:deep(.el-card__footer) {
    border: 0px;
    padding: 10px;
  }

  &:deep(.el-card__body) {
    flex-grow: 1;
    padding: 10px 0px;
    overflow: hidden;
    .el-descriptions {
      cursor: pointer;
      width: 96%;
      margin: 0px auto;
      padding: 8px;
      border-radius: 5px 5px 0px 0px;
      background-color: #f8f9fa;
    }
    .coupled-data {
      background: var(--el-color-danger-light-9);
      padding: 4px 9px 2px;
      margin: 0px auto 0px;
      width: 96%;
      border-radius: 0px 0px 5px 5px;
      color: var(--el-text-color-regular);
      font-size: 12px;
      line-height: 1.5em;
      .link-btn {
        color: var(--el-color-primary);
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
        line-height: 1.5em;
      }
    }
    .item-waper {
      margin-bottom: 20px;
      border-radius: 5px;
      overflow: hidden;
    }
  }
  .dis-flex {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .chat-list {
      width: 100%;
      flex-grow: 1;
      .mark-color {
        color: green;
        white-space: normal;
        text-align: left;
        line-height: 1.5rem;
      }
    }
  }
  .pagination {
    float: right;
  }
  .sort-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 5px;
    &:deep(.el-icon) {
      margin-top: -5px;
      font-size: 16px;
    }
  }
}
.dc-list-title {
  margin-left: 2px;
  span {
    display: inline-block;
    height: 32px;
    line-height: 32px;
  }
  .mr-36 {
    margin-right: 36px;
  }
}
.base-info-form {
  .el-form-item {
    margin: 8px 10px 15px;
  }
}
</style>
<style lang="scss">
// 标签全选 隐藏第一个自带的checkbox
.tagsCascaderClass {
  .el-cascader-menu:first-child {
    li.el-cascader-node:first-child {
      span.el-checkbox:first-child {
        display: none;
      }
    }
  }
}
</style>
