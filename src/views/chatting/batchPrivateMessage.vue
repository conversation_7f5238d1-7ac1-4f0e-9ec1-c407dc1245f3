<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <!-- 游戏 -->
        <el-form-item :label="`${$t('text_game')}：`">
          <el-select
            v-model="searchForm.project"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="(v, index) in gameList"
              :key="index"
              :label="v.app_name"
              :value="v.game_project"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 日期 -->
        <el-form-item :label="`${$t('text_date')}：`">
          <el-date-picker
            v-model="searchForm.date"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('start_date')"
            :end-placeholder="$t('end_date')"
            style="width: 200px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <!-- 创建人 -->
        <el-form-item :label="`${$t('text_founder')}：`">
          <el-select
            v-model="searchForm.operator"
            :placeholder="$t('place_select')"
            filterable
            multiple
            collapse-tags
            :reserve-keyword="false"
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="(v, index) in maintainerList"
              :key="index"
              :label="v.account"
              :value="v.account"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{
            $t('btn_search')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="Download"
            @click="donwLoad"
            v-has="'batchPrivateMessage:download'"
            :loading="progState"
            :disabled="progState"
            >{{ $t('text_data_export') }}
            <span v-if="progState">{{ progressNum + '%' }}</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table
        ref="_table"
        :data-api="pathMessageList"
        :params="params"
        tooltip-effect="dark"
        class="custom-table"
      >
        <el-table-column
          v-for="(item, index) in columns"
          :key="item.prop + index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :align="item.align ? item.align : 'center'"
          :show-overflow-tooltip="item.showTooltip"
        >
          <template #default="scope">
            <template v-if="item.prop === 'reply_content'">
              <span v-if="scope.row[item.prop].content">{{
                scope.row[item.prop].content || '-'
              }}</span>
              <div v-else-if="scope.row[item.prop].file_url">
                <span v-if="isValidImageFormat(scope.row[item.prop].file_url)">
                  <el-image
                    style="width: 50px; height: 50px"
                    :src="scope.row[item.prop].file_url"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="[scope.row[item.prop].file_url]"
                    :initial-index="0"
                    fit="cover"
                    :preview-teleported="true"
                  />
                </span>
                <span v-else>
                  <el-link :href="scope.row[item.prop].file_url" :underline="false">{{
                    $t('text_cant_read')
                  }}</el-link></span
                >
              </div>
              <div v-else>{{ '--' }}</div>
            </template>
            <template v-else-if="item.prop === 'status'">
              <span>{{ handleStatusFormatter(scope.row.status) }}</span>
            </template>
            <template v-else-if="item.prop === 'count'">
              <p class="count-box">
                <span
                  ><span class="left-success">{{ $t('text_success') }}:</span
                  >{{ scope.row[item.prop].success_count }}</span
                >
                <span
                  ><span class="left-success">{{ $t('text_error') }}:</span
                  >{{ scope.row[item.prop].failed_count }}</span
                >
              </p>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('btn_op')" width="150" align="center">
          <template #default="scope">
            <el-popover
              placement="right"
              :title="$t('text_fail_uids')"
              :width="200"
              trigger="click"
              popper-style="max-height: 400px;overflow-y: auto;overflow-x: hidden;"
            >
              <template #reference>
                <el-button
                  link
                  :disabled="scope.row.fail_ids.length === 0"
                  @click="handleView(scope.row)"
                  >{{ $t('text_views') }}</el-button
                >
              </template>
              <span v-for="item in detailContent" :key="item" class="dis-block">{{ item }}</span>
            </el-popover>
            <el-button link type="primary" @click="handleDownLoadDetail(scope.row)">{{
              $t('text_down_details')
            }}</el-button>
          </template>
        </el-table-column>
      </ops-table>
    </div>
  </div>
</template>

<script lang="ts">
import { getUserLists } from '@/api/relationship';
import { pathMessageList, pathAllDown, pathDown } from '@/api/chatting';
import { useI18n } from 'vue-i18n';
import { useEnumStore, useAppStore } from '@/stores';
export default defineComponent({
  name: 'TagLibraryConfig',
  components: {
    ElMessage,
  },
});
interface SearchForm {
  project: string[];
  date: string[];
  operator: string[];
}
interface Column {
  prop: string;
  label: string;
  width?: string;
  showTooltip?: boolean;
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const _table = ref();
const maintainerList = ref([]);
const searchForm = ref<SearchForm>({
  project: [],
  operator: [],
  date: [],
});
const params = computed(() => {
  return {
    ...searchForm.value,
  };
});
const columns = computed((): Column[] => {
  return [
    { prop: 'task_id', label: 'id', width: '80' },
    { prop: 'project', label: $t('text_game') },
    {
      prop: 'reply_content',
      label: $t('text_batch_message_content'),
      width: '260',
      showTooltip: true,
    },
    { prop: 'status', label: $t('text_process_status') },
    { prop: 'total', label: $t('text_sending_quantity') },
    { prop: 'count', label: $t('text_result') },
    { prop: 'operator', label: $t('text_founder') },
    { prop: 'create_at', label: $t('text_create_time') },
    { prop: 'finished_at', label: $t('text_finish_times') },
  ];
});
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum);
const progState = ref<boolean>(false);
watch(progressNum, n => {
  progState.value = n < 100 && n > -1 ? true : false;
});
// 获取专员
const getMaintainer = () => {
  getUserLists({}).then((res: any) => {
    maintainerList.value = res;
  });
};
getMaintainer();

const searchHandle = () => {
  _table.value.getData();
};
const detailContent = ref<number[]>([]);
// 查看
const handleView = (row: Record<string, unknown>) => {
  detailContent.value = row.fail_ids as number[];
};
// 导出
const donwLoad = () => {
  pathAllDown({ ...searchForm.value, page: 1, page_size: 20 });
};
// 下载明细
const handleDownLoadDetail = (row: Record<string, unknown>) => {
  pathDown({ task_id: row.task_id });
};
// 表格状态格式化
const handleStatusFormatter = (status: number) => {
  const statusMap = {
    0: '未开始',
    10: '处理中',
    20: '处理失败',
    30: '处理成功',
  };
  return statusMap[status as keyof typeof statusMap] || '--';
};
const isValidImageFormat = (url: string): boolean => {
  const validExtensions = ['.png', '.jpg', '.jpeg', '.gif'];
  const urlLower = url.toLowerCase();
  for (const ext of validExtensions) {
    if (urlLower.endsWith(ext)) {
      return true;
    }
  }
  return false;
};
</script>
<style lang="scss" scoped>
.count-box {
  display: flex;
  align-items: center;
  flex-direction: column;
  .left-success {
    margin-right: 5px;
  }
}
.dis-block {
  display: block;
}
</style>
