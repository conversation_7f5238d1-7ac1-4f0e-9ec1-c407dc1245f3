<template>
  <div class="mail-statistical">
    <!-- 顶部筛选栏 -->
    <el-card class="filter-bar">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select v-model="mailbox" placeholder="请选择邮箱" clearable>
            <el-option v-for="item in mailboxes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="status" placeholder="邮件状态" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="handler" placeholder="请选择处理人" clearable>
            <el-option v-for="item in handlers" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-date-picker v-model="createTime" type="daterange" range-separator="至" start-placeholder="邮件创建时间" end-placeholder="邮件创建时间" />
        </el-col>
        <el-col :span="5">
          <el-date-picker v-model="replyTime" type="daterange" range-separator="至" start-placeholder="邮件回复时间" end-placeholder="邮件回复时间" />
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="8">
        <el-card>
          <div class="stat-title">邮件总量</div>
          <div class="stat-value">{{ statData.total }} <span class="stat-unit">封</span></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div class="stat-title">邮件完成量</div>
          <div class="stat-value">{{ statData.finished }} <span class="stat-unit">封</span></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div class="stat-title">邮件未完成量</div>
          <div class="stat-value">{{ statData.unfinished }} <span class="stat-unit">封</span></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <div class="chart-title">邮件处理趋势</div>
          <v-chart :option="lineOption" autoresize style="height: 300px;" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div class="chart-title">邮件完成情况</div>
          <v-chart :option="pieOption" autoresize style="height: 300px;" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { use } from 'echarts/core'
import VChart from 'vue-echarts'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components'

use([
  CanvasRenderer,
  LineChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
])

const mailbox = ref('')
const status = ref('')
const handler = ref('')
const createTime = ref<any>([null, null])
const replyTime = ref<any>([null, null])

const mailboxes = [
  { label: '全部邮箱', value: '' },
  { label: '邮箱A', value: 'A' },
  { label: '邮箱B', value: 'B' }
]
const statusOptions = [
  { label: '全部', value: '' },
  { label: '已完成', value: 'finished' },
  { label: '未完成', value: 'unfinished' }
]
const handlers = [
  { label: '全部', value: '' },
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' }
]

const statData = ref({
  total: 2847,
  finished: 2156,
  unfinished: 691
})

const lineOption = ref({
  tooltip: { trigger: 'axis' },
  legend: { data: ['总量', '已完成', '未完成'] },
  xAxis: { type: 'category', data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },
  yAxis: { type: 'value' },
  series: [
    { name: '总量', type: 'line', data: [400, 420, 410, 430, 440, 420, 380] },
    { name: '已完成', type: 'line', data: [300, 320, 310, 330, 340, 320, 280] },
    { name: '未完成', type: 'line', data: [100, 100, 100, 100, 100, 100, 100] }
  ]
})

const pieOption = ref({
  tooltip: { trigger: 'item' },
  legend: { orient: 'vertical', right: 10, top: 'center', data: ['已完成', '未完成'] },
  series: [
    {
      name: '邮件完成情况',
      type: 'pie',
      radius: ['60%', '80%'],
      avoidLabelOverlap: false,
      label: { show: false, position: 'center' },
      emphasis: { label: { show: true, fontSize: 18, fontWeight: 'bold' } },
      labelLine: { show: false },
      data: [
        { value: 2156, name: '已完成', itemStyle: { color: '#22C55E' } },
        { value: 691, name: '未完成', itemStyle: { color: '#FF7D1A' } }
      ]
    }
  ]
})

function onSearch() {
  // 查询逻辑
}
function onReset() {
  mailbox.value = ''
  status.value = ''
  handler.value = ''
  createTime.value = [null, null]
  replyTime.value = [null, null]
}
</script>

<style scoped>
.mail-statistical {
  padding: 20px;
}
.filter-bar {
  margin-bottom: 20px;
}
.stat-cards {
  margin-bottom: 20px;
}
.stat-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}
.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #222;
}
.stat-unit {
  font-size: 14px;
  color: #888;
  margin-left: 4px;
}
.charts-row {
  margin-top: 10px;
}
.chart-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}
</style> 