<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form ref="searchFormRef" :inline="true" :model="searchForm" size="small">
        <!-- 游戏 -->
        <el-form-item :label="`${$t('text_game')}：`" prop="project">
          <el-select v-model="searchForm.project" :placeholder="$t('place_select')" multiple collapse-tags
            collapse-tags-tooltip clearable filterable :reserve-keyword="false" style="width: 180px">
            <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
              :value="v.game_project"></el-option>
          </el-select>
        </el-form-item>
        <!-- 日期 -->
        <el-form-item :label="`${$t('text_date')}：`" prop="date">
          <el-date-picker v-model="searchForm.date" type="daterange" range-separator="~"
            :start-placeholder="$t('start_date')" :end-placeholder="$t('end_date')" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" />
        </el-form-item>
        <!-- 标签 -->
        <el-form-item :label="`${$t('text_tag')}：`" prop="tag">
          <el-select v-model="searchForm.tag_ids" :placeholder="$t('text_please_change_tag')" multiple collapse-tags
            collapse-tags-tooltip clearable filterable :reserve-keyword="false" style="width: 180px">
            <el-option v-for="(v, index) in discordTagData" :key="index" :label="v.tag_name"
              :value="v.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="data-wapper">
      <el-card class="table-wapper" shadow="never">
        <el-table ref="opsTable" :data="tableData" v-loading="loading" stripe size="small"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }" class="ops-el-table">
          <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop"
            :label="item.label" :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'"
            :show-overflow-tooltip="item.showTooltip">
            <template #default="scope">
              <span v-if="item.prop === 'reply_count_rate'">{{ scope.row.reply_count_rate }}%</span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div class="chart-wapper">
        <ops-chart :options="chartData"></ops-chart>
      </div>

    </div>
  </div>
</template>

<script lang="ts">
import { useEnumStore } from '@/stores'
import rfdc from 'rfdc'
import type { FormInstance } from 'element-plus'
import { discordTagList } from '@/api/chatting'
import { replyTimeData, replyTimeExport } from '@/api/report'
import { TREND_DATA } from './chartOptions/replyTime'
import { useI18n } from 'vue-i18n'
export default defineComponent({
  name: 'ReplyTime'
})
interface SearchForm {
  project: string[]
  date?: [string, string]
  tag_ids?: number[]
}
interface Column {
  prop: string
  label: string
  align?: string
  width?: string
  showTooltip?: boolean
}
</script>
<script setup lang="ts">
const deepClone = rfdc()
const { t: $t } = useI18n()
const searchFormRef = ref<FormInstance>()
// 默认选择当前7天
const now = new Date()
const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
const searchForm = ref<SearchForm>({
  project: [],
  date: [sevenDaysAgo.toISOString().slice(0, 10), now.toISOString().slice(0, 10)]
})
const gameList = computed(() => useEnumStore().gameList)

// 获取discord标签库
const discordTagData = ref<Array<{ id: number, tag_name: string }>>([])
discordTagList({
  status: 1
}).then((res: { data: Array<{ id: number, tag_name: string }> }) => {
  if (res && Array.isArray(res.data)) {
    discordTagData.value = res.data
  } else {
    console.error('Invalid response:', res)
  }
}).catch((error: unknown) => {
  console.error('Error discord lists:', error)
})

// 获取表格、绘图数据
const loading = ref(false)
const tableData = ref<Array<{
  row_name: string
  reply_count_rate: number
  reply_count_detail: number
}>>([])
const columns: Column[] = [
  { prop: 'row_name', label: $t('text_reply_date') },
  { prop: 'reply_count_detail', label: $t('text_reply_count') },
  { prop: 'reply_count_rate', label: $t('text_reply_rate') }
]

const chartData = ref({})
const searchHandle = () => {
  loading.value = true
  replyTimeData({
    project: searchForm.value.project,
    date: searchForm.value.date,
    tag_ids: searchForm.value.tag_ids
  }).then((res: any) => {
    tableData.value = res.reply_count_data
    const chartBase: any = deepClone(TREND_DATA)
    const data = res.reply_avg_data.map((item: { date: string, avg_reply_time: number}) => {
      return [item.date, item.avg_reply_time]
    })
    chartBase.series = [{
      type: 'line',
      name: '平均每次回复等待时长',
      smooth: true,
      symbolSize: 8,
      lineStyle: {
        color: '#80bda7',
        width: 2,
      },
      itemStyle: {
        color: '#4aa181',
      },
      data
    }]
    chartBase.toolbox.feature.myDownload.onclick = () => {
      console.log('download')
      replyTimeExport({
        project: searchForm.value.project,
        date: searchForm.value.date,
        tag_ids: searchForm.value.tag_ids
      })
    }
    chartBase.legend.data = ['平均每次回复等待时长']
    chartBase.xAxis.data = res.reply_avg_data.map((item: { date: string }) => item.date)
    
    chartData.value = chartBase
  }).catch((error: unknown) => {
    console.error('Error reply time data:', error)
  }).finally(() => {
    loading.value = false
  })
}
searchHandle()
</script>

<style lang="scss" scoped>
.data-wapper {
  padding: 10px 8px;
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  .el-card {
    margin-bottom: 20px;
    &::v-deep(.el-card__body) {
      padding: 0;
    }
  }
  .chart-wapper {
    flex-grow: 1;
    margin-bottom: 0px;
    .el-card {
      height: 100%;
      &::v-deep(.el-card__body) {
        height: 100%;
      }
    }
  }
}
</style>