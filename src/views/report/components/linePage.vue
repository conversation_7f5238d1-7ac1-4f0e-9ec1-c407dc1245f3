<template>
  <div class="search-box">
    <el-form ref="searchFormRef" :inline="true" :model="searchForm" size="small">
      <!-- 游戏 -->
      <el-form-item :label="`${$t('text_game')}：`" prop="project">
        <el-select v-model="searchForm.project" :placeholder="$t('place_select')" clearable filterable style="width: 180px" @change="handleGame">
          <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
            :value="v.game_project"></el-option>
        </el-select>
      </el-form-item>
      <!-- 日期 -->
      <el-form-item :label="`${$t('text_date')}：`" prop="commu_date">
        <el-date-picker
          v-model="searchForm.commu_date"
          type="daterange"
          range-separator="~"
          :start-placeholder="$t('start_date')"
          :end-placeholder="$t('end_date')"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 220px"
        />
      </el-form-item>
      <!-- 处理人 -->
      <el-form-item :label="`${$t('text_processed_by')}：`" prop="operator">
        <el-select v-model="searchForm.operator" :placeholder="$t('place_select')" multiple collapse-tags
          collapse-tags-tooltip filterable :reserve-keyword="false" clearable style="width: 130px">
          <el-option v-for="(v, index) in personList" :key="index" :label="v.account"
            :value="v.account"></el-option>
        </el-select>
      </el-form-item>
      <!-- 处理状态 -->
      <el-form-item :label="`${$t('text_process_status')}：`" prop="handle_status">
        <el-select v-model="searchForm.handle_status" :placeholder="$t('place_select')" multiple collapse-tags
          collapse-tags-tooltip clearable style="width: 100px">
          <el-option v-for="(v, index) in enumList.QuestionHandleStatus" :key="index" :label="v.name"
            :value="v.value"></el-option>
        </el-select>
      </el-form-item>
      <!-- 问题类型 -->
      <el-form-item :label="`${$t('text_question_type_manage')}：`" prop="cat_id">
        <el-cascader v-model="searchForm.cat_id" style="width:100%" :options="questionOpts" filterable
          :disabled="!searchForm.project"
          :reserve-keyword="false"
          :placeholder="$t('place_select')"
          :props="{ checkStrictly: true, value: 'id', label: 'label', emitPath: false }" clearable>
        </el-cascader>
      </el-form-item>
      <!-- uid -->
      <el-form-item label="uid：" prop="uid">
        <el-input prefix-icon="Memo" v-model="searchForm.uid" :placeholder="$t('know_m_rich_placeholder')"  @input="handleInput" clearable />
      </el-form-item>
      <!-- 新增标签 -->
      <el-form-item :label="$t('text_tag')" prop="label">
        <el-cascader v-model="searchForm.label" style="width:100%"
          :disabled="!searchForm.project"
          :options="discordTagData" filterable
          :placeholder="$t('text_tag_placeholder')"
          collapse-tags
          :reserve-keyword="false"
          collapse-tags-tooltip
          :max-collapse-tags="1"
          :props="{ multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }" clearable>
        </el-cascader>
      </el-form-item>
      <!-- 查询 -->
      <el-form-item>
        <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
      </el-form-item>
      <!-- 下载 -->
      <el-form-item>
        <el-button type="primary" icon="Download" @click="donwLoad" v-has="'communicate:line:download'"
          :loading="progState" :disabled="progState">{{ $t('btn_download') }}
          <span v-if="progState">{{ progressNum + '%' }}</span>
        </el-button>
      </el-form-item>
      <!-- 问题类型 -->
      <el-form-item>
        <el-button type="primary" icon="EditPen" @click="handleQuestion">{{ $t('text_question_type_manage') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="page-content-box">
    <ops-table ref="_table" :data-api="lineCommunicateData" :params="params" tooltip-effect="dark" class="custom-table">
      <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
        :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'"
        :show-overflow-tooltip="item.showTooltip">
        <template #default="scope">
          <template v-if="item.prop === 'question_type'">
            <span>{{ statusFormatter(scope.row.question_type) }}</span>
          </template>
          <template v-else-if="item.prop === 'handle_status'">
            <span>{{ handleStatusFormatter(scope.row.handle_status) }}</span>
          </template>
          <template v-else-if="item.prop === 'remark'">
            <el-button size="small" bg text @click="handleSeeRemark(scope.row)">{{ $t('text_views') }}</el-button>
          </template>
          <template v-else-if="item.prop === 'dialogue'">
            <el-button size="small" bg text @click="handleDialogue(scope.row)">{{ $t('text_views') }}</el-button>
          </template>
          <template v-else-if="item.prop === 'label'">
            <!-- 添加逗号及换行 -->
            <div v-html="scope.row?.label?.map((item: any) => item.tag_name).join(',<br>') || '--'"></div>
          </template>
          <template v-else>
            {{ scope.row[item.prop] || '--' }}
          </template>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('btn_op')" width="80" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="editHandle(scope.row)">{{ $t('btn_edit') }}</el-button>
        </template>
      </el-table-column>
    </ops-table>
  </div>
  <dialogue v-if="dialogueVisible" v-model:visible="dialogueVisible" :params-data="dialogueList"></dialogue>
  <editDialogue v-if="editDialogueVisible" v-model:visible="editDialogueVisible" :edit-data="dialogueData" :cat-type="catType" @success="searchHandle"></editDialogue>
  <editQuestion v-if="editQuestionVisible" v-model:visible="editQuestionVisible" :edit-data="dialogueData" :cat-type="catType"></editQuestion>
  <seeRemark v-if="remarkVisible" v-model:visible="remarkVisible" :remark-list="remarkList"></seeRemark>
</template>

<script lang="ts">
import type { FormInstance } from 'element-plus'
import { lineCommunicateData, lineCommunicateDown, questionTypeList } from '@/api/report'
import { getUserLists } from '@/api/relationship'
import { getTagList } from '@/api/chatting'
import { useI18n } from 'vue-i18n'
import { useEnumStore, useAppStore } from '@/stores'
import dialogue from './dialogue.vue'
import editDialogue from './editDialogue.vue'
import editQuestion from './editQuestion.vue'
import seeRemark from './seeRemark.vue'
export default defineComponent({
  name: 'TagLibraryConfig',
  components: {
    ElMessage,
    dialogue,
    editDialogue,
    editQuestion,
    seeRemark
  }
})
interface SearchForm {
  project: string
  commu_date: string[]
  operator: string[]
  handle_status: number[]
  uid?: number,
  cat_type: number
}
interface Column {
  prop: string
  label: string
  width?: string
  showTooltip?: boolean
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const gameList = computed(() => useEnumStore().gameList)
const enumList = computed(() => useEnumStore().enumList)
const personList= ref([])
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum)
const progState = ref<boolean>(false)
watch(progressNum, (n) => {
  progState.value = (n < 100 && n > -1) ? true : false
})
const _table = ref()
const searchFormRef = ref<FormInstance>()
const questionOpts = ref([])
const searchForm = ref<SearchForm>({
  project: '',
  commu_date: [],
  operator: [],
  handle_status: [],
  cat_type: 2,
  label: []
})
const params = computed(() => {
  return {
    ...searchForm.value,
    uid: Number(searchForm.value.uid)
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'commu_date', label: $t('text_date'), width: '120'},
    { prop: 'project', label: $t('text_linked_game') },
    { prop: 'uid', label: 'uid' },
    { prop: 'sid', label: $t('text_server') },
    { prop: 'label', label: $t('text_tag'), width: '240' },
    { prop: 'nick_name', label: $t('text_line_nick') },
    { prop: 'pay_all', label: $t('text_payment_amount'), width: '100' },
    { prop: 'question', label: $t('text_communication_problem'), width: '260', showTooltip: true },
    { prop: 'category', label: $t('text_qtype')},
    { prop: 'handle_status', label: $t('text_process_status')},
    { prop: 'remark', label: $t('text_remark'), showTooltip: true },
    { prop: 'dialogue', label: $t('text_involving_dialogue')},
    { prop: 'operator', label: $t('text_operator')},
    { prop: 'maintainer', label: $t('text_correlation_commissioner')},
  ]
})
// 获取discord标签库
const discordTagData = ref<LabelObject[]>([])
const getDiscordTag = async() => {
  if(!searchForm.value.project) return
  try {
    const res = await getTagList({ project_name: searchForm.value.project, lib_type: 3 })
    if (res && Array.isArray(res.data)) {
      discordTagData.value = res.data
    } else {
      console.error('Invalid response:', res)
    }
  } catch (error) {
    console.error('Error discord lists:', error)
  }
}
// 获取处理人
const getMaintainer =() => {
  getUserLists({}).then((res: any) => {
    personList.value = res
  })
}
getMaintainer()
// 查询
const searchHandle = () => {
  _table.value.getData()
}
// 获取标签配置列表
const getTagConfigList = () => {
  const { project } = searchForm.value
  if(project) {
    questionOpts.value = []
    questionTypeList({
      project,
      cat_type: 2   // 问题分类 dc是1，line是2
    }).then((res: any) => {
      questionOpts.value = res.data
    })
  }
}
// 获取游戏
const handleGame = (val: string) => {
  getTagConfigList()
  getDiscordTag()
}
// 重置
// const reset = () => {
//   searchFormRef.value?.resetFields()
//   searchHandle()
// }
// 下载
const donwLoad = () => {
  const { uid } = searchForm.value
  const params = Object.assign({}, searchForm.value, {
    uid: Number(uid),
    page: 1,
    page_size: 20
  })
  lineCommunicateDown(params)
}
// 问题类型
const editQuestionVisible = ref(false)
const handleQuestion = () => {
  searchForm.value.project = '' // 重置游戏,触发获取问题类型
  editQuestionVisible.value = true
  catType.value = 2
}
// 查看备注
const remarkVisible = ref(false)
const remarkList = ref('')
const handleSeeRemark = (row: any) => {
  remarkVisible.value = true
  remarkList.value = ''
  const { remark } = row
  remarkList.value = remark
}
// 查看对话
const dialogueVisible = ref(false)
const dialogueList = ref<string[]>([])
const handleDialogue = (row: any) => {
  dialogueVisible.value = true
  dialogueList.value = []
  const { dialogue } = row
  // 过滤函数
  const filteredData = dialogue.filter((item: { content: string }) => {
    // 检查content是否非空
    return item.content.trim() !== ''
  })
  dialogueList.value.push(filteredData)
}
// 编辑
const dialogueData = ref({})
const editDialogueVisible = ref(false)
const catType = ref<number>()
const editHandle = (row: Record<string, unknown>) => {
  dialogueData.value = row
  editDialogueVisible.value = true
  catType.value = 2
}
const statusFormatter = (questionType: number) => {
  const statusMap = {
    1: '游戏咨询',
    2: '游戏建议',
    3: '游戏异常',
    4: '服务器匹配&合服',
    5: '抱怨/负面反馈',
  }
  return statusMap[questionType as keyof typeof statusMap] || '其他问题'
}
const handleStatusFormatter = (status: number) => {
  const statusMap = {
    1: '处理中',
    2: '已完成',
  }
  return statusMap[status as keyof typeof statusMap] || '其他'
}
const handleInput = (value: any) => {
  // 使用正则表达式匹配数字，并更新值
  searchForm.value.uid = value.replace(/[^\d]/g, '')
}
</script>
<style lang="scss" scoped>
.custom-table {
  .el-popper{font-size: 14px; max-width:20% }
}
.el-dialog__body {
  .content-height {
    max-height: 450px;
    overflow-y: auto;
    .ql-align-right {
      img {
        width: 200px;
        height: 200px;
      }
    }
  }
}
</style>
