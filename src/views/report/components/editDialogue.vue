<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_edit_communication_records')"
    :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <!-- uid -->
      <el-form-item label="uid" prop="uid">
        <el-input v-model="form.uid" disabled />
      </el-form-item>
      <!-- 昵称 -->
      <el-form-item :label="catType === 2 ? $t('text_line_nick') : $t('text_dc_nick')" prop="nick_name">
        <el-input v-model="form.nick_name" disabled />
      </el-form-item>
      <!-- 日期 -->
      <el-form-item :label="$t('text_time')" prop="commu_date">
        <el-date-picker
          v-model="form.commu_date"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :placeholder="$t('place_select')"
        />
      </el-form-item>
      <!-- 沟通问题 -->
      <el-form-item :label="$t('text_communication_problem')" prop="question">
        <el-input v-model="form.question" clearable :placeholder="$t('place_input')" />
      </el-form-item>
      <!-- 问题类型 -->
      <el-form-item :label="$t('text_qtype')" prop="cat_id">
        <el-cascader v-model="form.cat_id" style="width:100%" :options="questionList" filterable
          :placeholder="$t('text_tag_placeholder')"
          collapse-tags
          :reserve-keyword="false"
          collapse-tags-tooltip
          :max-collapse-tags="1"
          :props="{ checkStrictly: true, emitPath: false, value: 'id', label: 'label' }" clearable>
        </el-cascader>
      </el-form-item>
      <!-- 处理状态 -->
      <el-form-item :label="$t('text_process_status')" prop="handle_status">
        <el-select v-model="form.handle_status" :placeholder="$t('place_select')" clearable>
          <el-option v-for="(v, index) in enumList.QuestionHandleStatus" :key="index" :label="v.name"
            :value="v.value"></el-option>
        </el-select>
      </el-form-item>
      <!-- 备注 -->
      <el-form-item :label="$t('text_remark')" prop="remark">
        <opsEditer v-model="form.remark" v-model:uploading="isUploading"></opsEditer>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { useEnumStore } from '@/stores'
import { editCommunicate, lineEditCommunicate, questionTypeList } from '@/api/report'
export default defineComponent({
  name: 'CreateDrawer',
  components: {
    ElMessage
  }
})
interface EditTempLibProps {
  visible: boolean
  editData?: Record<string, unknown>
  catType: number
}
interface Form {
  id: number
  project: string
  uid?: number
  nick_name?:string
  commu_date?: string
  question?: string
  cat_id?: number
  handle_status?: number
  remark?: string
  cat_type: number
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n()
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  editData: () => ({}),
})
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'success'): void
}>()
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
})
const close = () => {
  value.value = false
}

const enumList = computed(() => useEnumStore().enumList)
const loading = ref(false)
const isUploading = ref(false)
const questionList = ref([])
const formRef = ref<FormInstance>()
const form = reactive<Form>({} as Form)

const submit = () => {
  formRef.value!.validate(async (valid) => {
    if (!valid || loading.value ) return
    loading.value = true
    try {
      await (props.catType === 2 ? lineEditCommunicate : editCommunicate)({ ...form, cat_type: props.catType })
      ElMessage.success($t('text_success'))
      emit('success')
      close()
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  })
}
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.id = props.editData.id as number
    form.project = props.editData.project as string
    form.uid = props.editData.uid as number
    form.nick_name = props.editData.nick_name as string
    form.commu_date = props.editData.commu_date as string
    form.question = props.editData.question as string
    form.cat_id = props.editData.cat_id as number
    form.handle_status = props.editData.handle_status as number
    form.remark = props.editData.remark as string
    // 获取问题类型
    questionTypeList({ 
      project: form.project,
      cat_type: props.catType
    }).then((res: any) => {
      questionList.value = res.data
    }).finally(()=> {
    })
  }
})
</script>

<style lang="scss" scoped></style>