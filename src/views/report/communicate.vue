<template>
  <div class="page-view-wapper">
    <el-tabs v-model="activeName">
      <el-tab-pane label="DC" name="dc">
        <dcPage />
      </el-tab-pane>
      <el-tab-pane label="LINE" name="line">
        <linePage />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import dcPage from './components/dcPage.vue'
import linePage from './components/linePage.vue'
export default defineComponent({
  name: 'Communicate',
  components: {
    dcPage,
    linePage
  }
})
</script>
<script setup lang="ts">
const activeName = ref('dc')
</script>
<style lang="scss" scoped>
.page-view-wapper {
  &:deep(.el-tabs) {
    height: 100%;
    padding: 0 20px;
  }
  &:deep(.el-tabs__content) {
    height: calc(100% - 65px)!important;
    overflow: auto;
  }
  &:deep(.el-scrollbar__view) {
    height: calc(100vh - 320px)!important;
  }
}
</style>