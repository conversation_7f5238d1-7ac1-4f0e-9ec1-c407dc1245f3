/**
 * 具体开发项目路由
 * @exports Array
 */

// 布局
const Layout = () => import( /* webpackChunkName: "Layout" */'@/layout/index.vue')
//首页-总览
const Overview = () => import( /* webpackChunkName: "HomePage" */'@/views/homePage/overview.vue')
//聊天-Discord
const Discord = () => import( /* webpackChunkName: "Chatting" */'@/views/chatting/discord.vue')
const BatchPrivateMessage = () => import( /* webpackChunkName: "Chatting" */'@/views/chatting/batchPrivateMessage.vue')
// 聊天-Line
const Line = () => import( /* webpackChunkName: "Chatting" */'@/views/chatting/line.vue')
// 聊天-Mail
const MailChat = () => import( /* webpackChunkName: "Chatting" */'@/views/chatting/mail.vue')
const MailStatistical = () => import( /* webpackChunkName: "Chatting" */'@/views/chatting/mailStatistical.vue')
// 系统配置
const TagLibraryConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/tagLibraryConfig.vue')
const EditTagLibraryConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/editTagLibraryConfig.vue')
const AssignConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/assignConfig.vue')
const TeamConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/teamConfig.vue')
const TemplateConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/templateConfig.vue')
const TimeoutTemplateConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/timeoutTemplateConfig.vue')
const Relationship = () => import( /* webpackChunkName: "Configure" */'@/views/configure/relationship.vue')
const DCBotConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/dcBotConfig.vue')
const ReplyTemplate = () => import( /* webpackChunkName: "Configure" */'@/views/configure/replyTemplate.vue')
const QCscoringForm = () => import( /* webpackChunkName: "Configure" */'@/views/configure/QCscoringForm.vue')
const QCtask = () => import( /* webpackChunkName: "Configure" */'@/views/configure/QCtask.vue')
const GameGoodsConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/gameGoodsConfig.vue')
const QuestionConfig = () => import( /* webpackChunkName: "Configure" */'@/views/configure/questionnaireConfig.vue')
const TicketCorpus = () => import( /* webpackChunkName: "Configure" */'@/views/configure/ticketCorpus.vue')
const TrainingResults = () => import( /* webpackChunkName: "Configure" */'@/views/configure/trainingResults.vue')
const AIStrategy = () => import( /* webpackChunkName: "Configure" */'@/views/configure/aiStrategy.vue')
// 数据报表
const Interaction = () => import( /* webpackChunkName: "DataReport" */'@/views/report/interaction.vue')
const MessageVolume = () => import( /* webpackChunkName: "DataReport" */'@/views/report/messageVolume.vue')
const Communicate = () => import( /* webpackChunkName: "DataReport" */'@/views/report/communicate.vue')
const ReplyTime = () => import( /* webpackChunkName: "DataReport" */'@/views/report/replyTime.vue')
const Satisfaction = () => import( /* webpackChunkName: "DataReport" */'@/views/report/satisfaction.vue')
// 质检
const DiscordQC = () => import( /* webpackChunkName: "QualityControl" */'@/views/qualityControl/discordQC.vue')
const TicketsQC = () => import( /* webpackChunkName: "QualityControl" */'@/views/qualityControl/ticketsQC.vue')
// 问卷调查
const Questionnaire = () => import( /* webpackChunkName: "QualityControl" */'@/views/questionNaire/questionnaire.vue')
const projectRouters = [
  { // 首页
    path: '/homePage',
    name: 'HomePage',
    component: Layout,
    redirect: '/homePage/overview',
    meta: { title: 'text_ticket', icon: 'Tickets', parent: 'HomePage' },
    children: [
      // 工单总览
      {
        path: '/homePage/overview',
        name: 'Overview',
        component: Overview,
        meta: { title: 'menu_tickets_overview', icon: 'Tickets', parent: 'HomePage' }
      }
    ]
  },
  { // 聊天
    path: '/chatting',
    name: 'Chatting',
    component: Layout,
    redirect: '/chatting/discord',
    meta: { title: 'Discord', icon: 'ChatDotRound', parent: 'Chatting' },
    children: [
      // Discord
      {
        path: '/chatting/discord',
        name: 'Discord',
        component: Discord,
        meta: { title: 'menu_discord', icon: 'ChatDotRound', parent: 'Chatting' }
      },
      { //批量私信
        path: '/chatting/batchPrivateMessage',
        name: 'BatchPrivateMessage',
        component: BatchPrivateMessage,
        meta: { title: 'text_batch_private_message', icon: 'Message', parent: 'Chatting' }
      }
    ]
  },
  { // Line
    path: '/lineChat',
    name: 'LineChat',
    component: Layout,
    redirect: '/lineChat/line',
    meta: { title: 'Line', icon: 'ChatLineSquare', parent: 'LineChat' },
    children: [
      // Line
      {
        path: '/lineChat/line',
        name: 'Line',
        component: Line,
        meta: { title: 'Line', icon: 'ChatLineSquare', parent: 'LineChat' }
      }
    ]
  },
  { // Mail
    path: '/mailChat',
    name: 'MailChatLayout',
    component: Layout,
    redirect: '/mailChat/mail',
    meta: { title: 'menu_mail', icon: 'Message', parent: 'MailChat' },
    children: [
      {
        path: '/mailChat/mail',
        name: 'MailChat',
        component: MailChat,
        meta: { title: 'menu_mail', icon: 'Message' }
      },
      {
        path: '/mailChat/statistical',
        name: 'MailStatistical',
        component: MailStatistical,
        meta: { title: 'menu_mail_statistical', icon: 'DataAnalysis' }
      }
    ]
  },
  { // 质检
    path: '/qualityControl',
    name: 'QualityControl',
    component: Layout,
    redirect: '/qualityControl/discordQC',
    meta: { title: 'menu_quality_control', icon: 'Notification', parent: 'QualityControl' },
    children: [
      // discord质检
      {
        path: '/qualityControl/discordQC',
        name: 'DiscordQC',
        component: DiscordQC,
        meta: { title: 'menu_discord_QC', icon: 'Pointer', parent: 'QualityControl' }
      },
      // 工单质检
      {
        path: '/qualityControl/ticketsQC',
        name: 'TicketsQC',
        component: TicketsQC,
        meta: { title: 'menu_tickets_QC', icon: 'Tickets', parent: 'QualityControl' }
      }
    ]
  },
  { // 配置
    path: '/configure',
    name: 'Configure',
    component: Layout,
    redirect: '/configure/tagLibrary',
    meta: { title: 'menu_sys_config', icon: 'Setting', parent: 'Configure' },
    children: [
      // 标签库配置
      {
        path: '/configure/tagLibraryConfig',
        name: 'TagLibraryConfig',
        component: TagLibraryConfig,
        meta: { title: 'menu_tag_lib', icon: 'PriceTag', parent: 'Configure' }
      },
      // 标签库编辑配置
      {
        path: '/configure/editTagLibraryConfig',
        name: 'EditTagLibraryConfig',
        component: EditTagLibraryConfig,
        meta: { title: 'menu_tag_lib', icon: 'CollectionTag', parent: 'Configure', hidden: true },
      },
      // 分单配置
      {
        path: '/configure/assignConfig',
        name: 'AssignConfig',
        component: AssignConfig,
        meta: { title: 'menu_sys_assign', icon: 'Pointer', parent: 'Configure' }
      },
      // 团队配置
      {
        path: '/configure/teamConfig',
        name: 'TeamConfig',
        component: TeamConfig,
        meta: { title: 'menu_team_config', icon: 'User', parent: 'Configure' }
      },
      // 模板配置
      {
        path: '/configure/templateConfig',
        name: 'TemplateConfig',
        component: TemplateConfig,
        meta: { title: 'menu_sys_template_assign', icon: 'Memo', parent: 'Configure' }
      },
      // 玩家维护关系配置
      {
        path: '/configure/relationship',
        name: 'Relationship',
        component: Relationship,
        meta: { title: 'menu_sys_new_relationship', icon: 'Connection', parent: 'Configure' }
      },
      // DC调查问卷配置
      {
        path: '/configure/questionConfig',
        name: 'QuestionConfig',
        component: QuestionConfig,
        meta: { title: 'text_discord_question_config', icon: 'Star', parent: 'Configure' }
      },
      // 自动回复模板配置
      {
        path: '/configure/replyTemplate',
        name: 'ReplyTemplate',
        component: ReplyTemplate,
        meta: {
          title: 'menu_sys_autoreply_template_config',
          icon: 'CopyDocument',
          parent: 'Configure',
        },
      },
      // 超时提醒模板配置
      {
        path: '/configure/timeoutTemplateConfig',
        name: 'TimeoutTemplateConfig',
        component: TimeoutTemplateConfig,
        meta: {
          title: 'menu_sys_timeout_template_config',
          icon: 'Clock',
          parent: 'Configure',
        },
      },
      // 质检打分表单配置
      {
        path: '/configure/QCscoringForm',
        name: 'QCscoringForm',
        component: QCscoringForm,
        meta: {
          title: 'menu_sys_QCscoringForm_config',
          icon: 'Collection',
          parent: 'Configure',
        },
      },
      // 质检任务配置
      {
        path: '/configure/QCtask',
        name: 'QCtask',
        component: QCtask,
        meta: {
          title: 'menu_sys_QCtask_config',
          icon: 'Loading',
          parent: 'Configure',
        },
      },
      // 游戏道具配置
      {
        path: '/configure/gameGoods',
        name: 'GameGoodsConfig',
        component: GameGoodsConfig,
        meta: {
          title: 'menu_sys_gameGoods_config',
          icon: 'Aim',
          parent: 'Configure',
        },
      },
      // 工单优质语料库
      {
        path: '/configure/ticketCorpus',
        name: 'TicketCorpus',
        component: TicketCorpus,
        meta: {
          title: 'menu_sys_ticket_corpus',
          icon: 'Document',
          parent: 'Configure',
        },
      },
      // 训练结果
      {
        path: '/configure/trainingResults',
        name: 'TrainingResults',
        component: TrainingResults,
        meta: {
          title: 'menu_sys_training_results',
          icon: 'DataAnalysis',
          parent: 'Configure',
        },
      },
      // AI处理策略
      {
        path: '/configure/aiStrategy',
        name: 'AIStrategy',
        component: AIStrategy,
        meta: {
          title: 'menu_sys_ai_strategy',
          icon: 'Monitor',
          parent: 'Configure',
        },
      },
      // DC机器人配置
      {
        path: '/configure/dcBotConfig',
        name: 'DCBotConfig',
        component: DCBotConfig,
        meta: {
          title: 'menu_dc_bot_config',
          icon: 'Monitor',
          parent: 'Configure'
        }
      },
    ]
  },
  { // 数据报表
    path: '/report',
    name: 'Report',
    component: Layout,
    redirect: '/report/interaction',
    meta: { title: 'menu_report', icon: 'DataAnalysis', parent: 'Report' },
    children: [
      // 互动数据报表
      {
        path: '/report/interaction',
        name: 'Interaction',
        component: Interaction,
        meta: { title: 'menu_report_interaction', icon: 'Connection', parent: 'Report' }
      },
      // 消息量报表
      {
        path: '/report/messageVolume',
        name: 'MessageVolume',
        component: MessageVolume,
        meta: { title: 'menu_report_message', icon: 'ChatDotSquare', parent: 'Report' }
      },
      // 沟通记录报表
      {
        path: '/report/communicate',
        name: 'Communicate',
        component: Communicate,
        meta: { title: 'menu_sys_new_communication_records', icon: 'Document', parent: 'Report' }
      },
      // DC回复时间报表
      {
        path: '/report/replyTime',
        name: 'ReplyTime',
        component: ReplyTime,
        meta: { title: 'text_menu_reply_time_records', icon: 'Clock', parent: 'Report' }
      },
      // DC满意度报表
      {
        path: '/report/satisfied',
        name: 'Satisfaction',
        component: Satisfaction,
        meta: { title: 'text_discord_satisfied_report', icon: 'Collection', parent: 'Report' }
      }
    ]
  },
  // 问卷调查
  {
    path: '/questionnaire',
    name: 'Questionnaire',
    component: Questionnaire,
    meta: { title: 'text_discord_satisfied_report', icon: 'ChatLineSquare', hidden: true }
  }
]

export default projectRouters
