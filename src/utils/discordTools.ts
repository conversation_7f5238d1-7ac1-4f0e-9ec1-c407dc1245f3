export function isDiscordEmoji(emojiString: string) {
  // 检查是否以 <a: 或 <: 开头并以 > 结尾
  if ((emojiString.startsWith("<:") || emojiString.startsWith("<a:")) && emojiString.endsWith(">")) {
      // 区分动画表情和普通表情
      const isAnimated = emojiString.startsWith("<a:");

      // 提取中间部分，根据是否为动画表情确定起始位置
      const innerContent = isAnimated
          ? emojiString.slice(3, -1) // 去掉开头的 <a: 和结尾的 >
          : emojiString.slice(2, -1); // 去掉开头的 <: 和结尾的 >

      // 使用正则表达式检查格式
      const pattern = /^[\w-]+:\d+$/; // 表情名称由字母、数字、下划线和连字符组成，后面跟着一个冒号和数字
      return pattern.test(innerContent);
  }
  return false;
}

// 将discord表情转换为图片
export function convertDiscordEmojiToImage(emojiString: string) {
  try {
    // 判断是否为discord表情
  if (isDiscordEmoji(emojiString)) {
    // 判断是否为动画表情
    const isAnimated = emojiString.startsWith("<a:");

    // 提取表情名称和ID
    const match = emojiString.match(isAnimated ?
      /<a:([^:]+):(\d+)>/ :
      /<:([^:]+):(\d+)>/);
    if (match && match.length === 3) {
      const emojiId = match[2];
      // 构建Discord CDN URL
      // 动画表情使用.gif格式，静态表情使用.png格式
      const extension = isAnimated ? 'gif' : 'png';
      return `<img src="https://cdn.discordapp.com/emojis/${emojiId}.${extension}" alt="${emojiString}" />`;
      }
    }
    return emojiString;
  } catch (error) {
    return emojiString;
  }
}
