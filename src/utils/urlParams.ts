/**
 * URL参数处理工具
 * 用于解析URL参数中的project和ticket_ids，并存储到sessionStorage中
 */

const STORAGE_KEY = {
  PROJECT: 'ticket_url_project',
  TICKET_IDS: 'ticket_url_ticket_ids'
}

/**
 * 保存URL参数到sessionStorage
 * @param project 项目参数
 * @param ticketIds 工单ID参数
 */
export const saveUrlParamsToStorage = (project?: string | string[], ticketIds?: string | string[]) => {
  if (project) {
    // 将project转换为字符串存储
    if (Array.isArray(project)) {
      const projectStr = project.join(',')
      if (projectStr) {
        sessionStorage.setItem(STORAGE_KEY.PROJECT, projectStr)
      }
    } else if (project) {
      sessionStorage.setItem(STORAGE_KEY.PROJECT, project)
    }
  }

  if (ticketIds) {
    // 将ticketIds转换为字符串存储
    if (Array.isArray(ticketIds)) {
      const ticketIdsStr = ticketIds.join(',')
      if (ticketIdsStr) {
        sessionStorage.setItem(STORAGE_KEY.TICKET_IDS, ticketIdsStr)
      }
    } else if (ticketIds) {
      sessionStorage.setItem(STORAGE_KEY.TICKET_IDS, ticketIds)
    }
  }
}

/**
 * 从URL获取并保存参数
 */
export const saveUrlParamsFromRoute = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const project = urlParams.get('project')
  const ticketIds = urlParams.get('ticket_ids')

  if (project || ticketIds) {
    saveUrlParamsToStorage(project || '', ticketIds || '')
  }
}

/**
 * 从sessionStorage获取保存的参数
 * @returns 参数对象
 */
export const getParamsFromStorage = () => {
  return {
    project: sessionStorage.getItem(STORAGE_KEY.PROJECT) || '',
    ticketIds: sessionStorage.getItem(STORAGE_KEY.TICKET_IDS) || ''
  }
}

/**
 * 清除存储的参数
 */
export const clearStoredParams = () => {
  sessionStorage.removeItem(STORAGE_KEY.PROJECT)
  sessionStorage.removeItem(STORAGE_KEY.TICKET_IDS)
}
