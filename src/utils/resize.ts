/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2024-05-13 18:59:10 
 * @Last Modified by:   wenhao.wang 
 * @Last Modified time: 2024-05-13 18:59:10 
 */

import type { App, DirectiveBinding } from 'vue'
interface ResizeOptions {
  callback: (el: HTMLElement, width: number, height: number) => void
  mode: 'width' | 'height' | 'both'
}

const resizeDirective = {
  install: (app: App<Element>) => {
    app.directive('resize', {
      beforeMount(el: HTMLElement, binding: DirectiveBinding<ResizeOptions>) {
        let startHeight = 0
        let startY = 0
        let startWidth = 0
        let startX = 0
        let dragging = false

        const resizeHandle = document.createElement('div')
        resizeHandle.style.position = 'absolute'
        resizeHandle.style.bottom = '0'
        resizeHandle.style.right = '0'
        resizeHandle.style.width = '0px'
        resizeHandle.style.height = '0px'
        resizeHandle.style.borderLeft = '6px solid transparent'
        resizeHandle.style.borderTop = '6px solid transparent'
        resizeHandle.style.borderBottom = '6px solid lightgray'
        resizeHandle.style.borderRight = '6px solid lightgray'
        resizeHandle.style.background = 'transparent'
        resizeHandle.style.cursor = 'nwse-resize'
        resizeHandle.style.zIndex = '9999'
        el.appendChild(resizeHandle)

        const handleMouseDown = (e: MouseEvent) => {
          if (e.target === resizeHandle) {
            dragging = true
            startHeight = el.offsetHeight
            startY = e.pageY
            startWidth = el.offsetWidth
            startX = e.pageX
            document.addEventListener('mousemove', handleMouseMove)
            document.addEventListener('mouseup', handleMouseUp)
          }
        };

        const handleMouseMove = (e: MouseEvent) => {
          if (dragging) {
            const newHeight = startHeight + (e.pageY - startY)
            const newWidth = startWidth + (e.pageX - startX)
            if (binding.value.mode === 'height' || binding.value.mode === 'both') {
              el.style.height = `${newHeight}px`
            }
            if (binding.value.mode === 'width' || binding.value.mode === 'both') {
              el.style.width = `${newWidth}px`
            }
            if (typeof binding.value.callback === 'function') {
              binding.value.callback(el, newWidth, newHeight)
            }
          }
        }

        const handleMouseUp = () => {
          dragging = false
          document.removeEventListener('mousemove', handleMouseMove)
          document.removeEventListener('mouseup', handleMouseUp)
          el.style.cursor = ''
        }

        el.style.position = 'relative'
        el.style.overflow = 'hidden'
        el.addEventListener('mousedown', handleMouseDown)
      }
    })
  }
}

export default resizeDirective