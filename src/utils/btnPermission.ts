import type{ App } from 'vue'
import { useUserInfoStore } from '@/stores/user'

const hasPermission = {
  install: (app: App<Element>) => {
    app.directive('has', {
      mounted(el, binding) {
        const btnPermissions = useUserInfoStore().permissions.button
        const n = btnPermissions.filter(i => i.code === binding.value).length
        if (n === 0) {
          if (el.parentNode) {
            el.parentNode.removeChild(el)
          }
        }
      },
      updated(el, binding) {
        const btnPermissions = useUserInfoStore().permissions.button
        const n = btnPermissions.filter(i => i.code === binding.value).length
        if (n === 0) {
          if (el.parentNode) {
            el.parentNode.removeChild(el)
          }
        }
      }
    })
  }
}

export default hasPermission