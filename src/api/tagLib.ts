import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 保存标签库
export const saveTagLib: ApiT = params => FpRequest.post(`${API}/new/tag_lib/save`, params)
// 标签库列表
export const tagLibList: ApiT = params => FpRequest.post(`${API}/new/tag_lib/list`, params)
// 标签库禁启用
export const tagLibEnable: ApiT = params => FpRequest.post(`${API}/new/tag_lib/enable`, params)
// discord标签库列表
export const discordTagList: ApiT = params => FpRequest.post(`${API}/dsc/tag/list`, params)
// 保存discord标签库
export const saveDiscordTag: ApiT = params => FpRequest.post(`${API}/dsc/tag/save`, params)
// 编辑discord标签库
export const editDiscord: ApiT = params => FpRequest.post(`${API}/dsc/tag/edit`, params)
// 新增标签配置
export const configTagAdd: ApiT = params => FpRequest.post(`${API}/tag_config/add`, params)
// 修改标签配置
export const configTagEdit: ApiT = params => FpRequest.post(`${API}/tag_config/edit`, params)
// 删除标签配置
export const configTagDel: ApiT = params => FpRequest.post(`${API}/tag_config/del`, params)
// 查询标签配置列表
export const getConfigTagList: ApiT = params => FpRequest.post(`${API}/tag_config/list`, params)
