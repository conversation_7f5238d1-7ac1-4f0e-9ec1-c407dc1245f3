import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 交互报表数据
export const interactionData: ApiT = params => FpRequest.post(`${API}/dsc/interaction/stats`, params)
// 交互报表数据导出
export const interactionExport: ApiT = params => FpRequest.download(`${API}/dsc/interaction/detail/export`, 'post', params)
// 获取玩家UID
export const getPlayerUid: ApiT = params => FpRequest.post(`${API}/dsc/player/uid`, params)
// 时间信息量数据
export const timeMsgData: ApiT = params => FpRequest.post(`${API}/dsc/message/count/date/stats`, params)
// 处理人信息量数据
export const operatorMsgData: ApiT = params => FpRequest.post(`${API}/dsc/message/count/operator/stats`, params)
// 信息量明细导出
export const msgDetailExport: ApiT = params => FpRequest.download(`${API}/dsc/message/count/detail/export`, 'post', params)
// 沟通记录查询列表  1dc 2line；注意：line用line的 这三个改造的新接口后端两张表 获取沟通对话有问题
export const communicateData: ApiT = params => FpRequest.post(`${API}/dsc/new/commu/list`, params)
// 编辑沟通记录
export const editCommunicate: ApiT = params => FpRequest.post(`${API}/dsc/new/commu/edit`, params)
// 下载沟通记录表
// export const communicateDown: ApiT = params => FpRequest.download(`${API}/dsc/commu/list/export`, 'post', params)
export const communicateDown: ApiT = params => FpRequest.download(`${API}/dsc/new/commu/list/export`, 'post', params)
// 回复时长统计
export const replyTimeData: ApiT = params => FpRequest.post(`${API}/dsc/replytime/detail/stats`, params)
// 回复时长统计图标导出
export const replyTimeExport: ApiT = params => FpRequest.download(`${API}/dsc/replytime/detail/export`, 'post', params)
// 问题类型-添加类型
export const questionTypeAdd: ApiT = params => FpRequest.post(`${API}/channel/cat/add`, params)
// 问题类型-编辑类型
export const questionTypeEdit: ApiT = params => FpRequest.post(`${API}/channel/cat/save`, params)
// 问题类型-删除类型
export const questionTypeDel: ApiT = params => FpRequest.post(`${API}/channel/cat/del`, params)
// 问题类型-列表数据   问题分类 dc是1，line是2
export const questionTypeList: ApiT = params => FpRequest.post(`${API}/channel/cat/tree`, params)
// 满意度报表
export const satisfactionData: ApiT = params => FpRequest.post(`${API}/survey/satisfaction/date/stats`, params)
// 满意度报表导出
export const satisfactionExport: ApiT = params => FpRequest.download(`${API}/survey/satisfaction/date/stats_export`, 'post', params)

// line
// line沟通记录查询接口
export const lineCommunicateData: ApiT = params => FpRequest.post(`${API}/line/commu/list`, params)
// line沟通记录导出接口
export const lineCommunicateDown: ApiT = params => FpRequest.download(`${API}/line/commu/list/export`, 'post', params)
// 编辑沟通记录
export const lineEditCommunicate: ApiT = params => FpRequest.post(`${API}/line/commu/edit`, params)