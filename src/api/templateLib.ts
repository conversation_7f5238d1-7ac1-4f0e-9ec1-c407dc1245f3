import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 保存模板
export const saveTempLib: ApiT = params => FpRequest.post(`${API}/module/save`, params)
// 编辑模板&启用/禁用状态调用同一个接口
export const editEnableTempLib: ApiT = params => FpRequest.post(`${API}/module/edit`, params)
// 模板列表
export const tempLibList: ApiT = params => FpRequest.post(`${API}/module/list`, params)
// 模板列表options
export const tempLibOptions: ApiT = params => FpRequest.post(`${API}/module/options`, params)
// 删除
export const delTempLib: ApiT = params => FpRequest.post(`${API}/module/delete`, params)

// 自动回复模版配置
export const getReplyTemplateList: ApiT = params => FpRequest.post(`${API}/reply_tpl/list`, params)
export const enableReplyTemplate: ApiT = params => FpRequest.post(`${API}/reply_tpl/enable`, params)
export const addReplyTemplate: ApiT = params => FpRequest.post(`${API}/reply_tpl/add`, params)
export const editReplyTemplate: ApiT = params => FpRequest.post(`${API}/reply_tpl/save`, params)
export const replyTemplateInfo: ApiT = params => FpRequest.post(`${API}/reply_tpl/info`, params)

// 以下为模板类型相关接口：

// 模板类型列表
export const templateTypeList: ApiT = params => FpRequest.post(`${API}/module/cat/tree`, params)
// 编辑模板类型
export const editTemplateType: ApiT = params => FpRequest.post(`${API}/module/cat/save`, params)
// 添加模板类型
export const addTemplateType: ApiT = params => FpRequest.post(`${API}/module/cat/add`, params)
// 删除模板类型
export const delTemplateType: ApiT = params => FpRequest.post(`${API}/module/cat/del`, params)
