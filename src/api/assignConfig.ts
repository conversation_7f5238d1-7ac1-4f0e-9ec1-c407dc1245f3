import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 分单配置列表
export const assignConfigList: ApiT = params => FpRequest.post(`${API}/user_assign_ticket/list`, params)
// 保存分单配置
export const assignConfigSave: ApiT = params => FpRequest.post(`${API}/user_assign_ticket/add`, params)
// 编辑分单配置
export const assignConfigEdit: ApiT = params => FpRequest.post(`${API}/user_assign_ticket/edit`, params)
// 删除分单配置
export const assignConfigDelete: ApiT = params => FpRequest.post(`${API}/user_assign_ticket/del`, params)
// 获取客服列表
export const getAcceptorList: ApiT = params => FpRequest.post(`${API}/addons/user_list`, params)
// 获取团队成员列表
export const getTeamList: ApiT = params => FpRequest.post(`${API}/team_config/list`, params)
// 新增团队配置
export const teamConfigSave: ApiT = params => FpRequest.post(`${API}/team_config/add`, params)
// 编辑团队配置
export const teamConfigEdit: ApiT = params => FpRequest.post(`${API}/team_config/edit`, params)
// 删除团队配置
export const teamConfigDel: ApiT = params => FpRequest.post(`${API}/team_config/del`, params)
// 获取问题分类列表
export const getQuestionList: ApiT = params => FpRequest.post(`${API}/cat/opts`, params)