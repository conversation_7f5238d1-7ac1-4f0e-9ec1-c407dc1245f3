import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 新增
export const saveRelation: ApiT = params => FpRequest.post(`${API}/dsc/maintain/config/save`, params)
// 编辑
export const editRelationLib: ApiT = params => FpRequest.post(`${API}/dsc/maintain/config/edit`, params)
// 列表
export const relationList: ApiT = params => FpRequest.post(`${API}/dsc/maintain/config/list`, params)
// 删除
export const delRelationLib: ApiT = params => FpRequest.post(`${API}/dsc/maintain/config/del`, params)
// 导出
export const exportLib: ApiT = params => FpRequest.download(`${API}/dsc/maintain/config/list_export`, 'post', params)
// 获取玩家DC账号
export const getAccount: ApiT = params => FpRequest.post(`${API}/dsc/player/account`, params)
// 获取维护专员信息
export const getUserLists: ApiT = params => FpRequest.post(`${API}/addons/user_list`, params)
// discord玩家查询
export const getUserPool: ApiT = params => FpRequest.post(`${API}/dsc/user/pool`, params)

// Line
// 查询玩家关系维护配置接口
export const lineRelationList: ApiT = params => FpRequest.post(`${API}/line/maintain/config/list`, params)
// 查询玩家关系维护配置数据导出接口
export const lineExportLib: ApiT = params => FpRequest.download(`${API}/line/maintain/config/list_export`, 'post', params)
// 修改玩家关系维护配置
export const lineEditRelationLib: ApiT = params => FpRequest.post(`${API}/line/maintain/config/edit`, params)
// 删除玩家关系维护配置
export const lineDelRelationLib: ApiT = params => FpRequest.post(`${API}/line/maintain/config/del`, params)
// 下拉获取玩家Line账号信息
export const lineGetAccount: ApiT = params => FpRequest.post(`${API}/line/player/account`, params)