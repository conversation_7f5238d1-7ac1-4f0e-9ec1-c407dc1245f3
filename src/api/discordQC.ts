import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// discord质检单列表
export const discordQcTaskList: ApiT = params => FpRequest.post(`${API}/examine/order/dsc/list`, params)
// 获取质检打分表
export const getQcformTpl: ApiT = params => FpRequest.post(`${API}/examine/tpl/detail`, params)
// 获取质检单详情
export const getQcTaskDetail: ApiT = params => FpRequest.post(`${API}/examine/order/dsc/detail`, params)
// 质检单保存
export const saveQcTask: ApiT = params => FpRequest.post(`${API}/examine/order/dsc/save`, params)
// 概览数据
export const getQcOverview: ApiT = params => FpRequest.post(`${API}/examine/order/dsc/stats`, params)
// 质检单查询列表下载
export const qcTaskDownload: ApiT = params => FpRequest.download(`${API}/examine/order/dsc/list_export`, 'post', params)

