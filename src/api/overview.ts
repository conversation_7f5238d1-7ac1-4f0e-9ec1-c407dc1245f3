import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 工单数据总览
export const overview: ApiT = params => FpRequest.post(`${API}/ticket/count`, params)
// 获取渠道包列表
export const getChannelList: ApiT = params => FpRequest.post(`${API}/addons/channel_package_id`, params)
// 获取标签列表-lib_type传值，工单 1，DC 2，Line 3
export const getTagList: ApiT = params => FpRequest.post(`${API}/new/tags/opts`, params)
// 获取问题分类：老工单
export const questionOptsAdmin: ApiT = params => FpRequest.post(`/gateway/proxy/cs_ticket/all/api/cat/opts/admin`, params)
// 工单池列表
export const ticketPoolList: ApiT = params => FpRequest.post(`${API}/ticket/pool`, params)
// 工单池详情
export const ticketPoolDetail: ApiT = params => FpRequest.post(`${API}/ticket/info`, params)
// 指派接口
export const assignTicket: ApiT = params => FpRequest.post(`${API}/ticket/reassign`, params)
// 批量指派
export const batchAssign: ApiT = params => FpRequest.post(`${API}/ticket/batch_reassign`, params)
// 回复工单
export const replyTicket: ApiT = params => FpRequest.post(`${API}/ticket/transfer`, params)
// 工单升级降级切换
export const ticketChangeLevel: ApiT = params => FpRequest.post(`${API}/ticket/upgrade`, params)
// 工单流转
export const ticketTransfer: ApiT = params => FpRequest.post(`${API}/ticket/turn`, params)
// 添加备注
export const addRemark: ApiT = params => FpRequest.post(`${API}/ticket/remark`, params)
// 编辑标签
export const editTag: ApiT = params => FpRequest.post(`${API}/ticket/retagging`, params)
// 获取标签
export const getTag: ApiT = params => FpRequest.post(`${API}/ticket/tags`, params)
// 工单查询结果下载
export const ticketDownload: ApiT = params => FpRequest.download(`${API}/ticket/export`, 'post', params)

// AI总结
export const aiSummary: ApiT = params => FpRequest.post(`${API}/ai/summary`, params)
// AI润色
export const aiPolishApi: ApiT = params => FpRequest.post(`${API}/ai/polish`, params)
// AI预回复
export const aiPreReply: ApiT = params => FpRequest.post(`${API}/ai/pre_reply`, params)
// 回复&关单入库 获取问题和答案
export const aiFaq: ApiT = params => FpRequest.post(`${API}/ai/faq`, params)
// 退回工单池
export const returnPool: ApiT = params => FpRequest.post(`${API}/ticket/return_pool`, params)
// 工单备注草稿保存
export const draftSave: ApiT = params => FpRequest.post(`${API}/ticket/draft/save`, params)
// 工单备注草稿信息
export const draftInfo: ApiT = params => FpRequest.post(`${API}/ticket/draft/info`, params)

// 批量打标签
export const batchTagSave: ApiT = params => FpRequest.post(`${API}/ticket/batch_retagging`, params)
// 工单批量回复/回复&关单/拒单
export const ticketBatchReply: ApiT = params => FpRequest.post(`${API}/ticket/batch_transfer`, params)
// 添加Tab
export const addTicketTab: ApiT = params => FpRequest.post(`${API}/ticket/tab/save`, params)
// 编辑Tab
export const editTicketTab: ApiT = params => FpRequest.post(`${API}/ticket/tab/edit`, params)
// 搜索tab列表
export const getTicketTabLists: ApiT = params => FpRequest.post(`${API}/ticket/tab/list`, params)
// 设置搜索tab排序
export const updateTabSettingOrder: ApiT = params => FpRequest.post(`${API}/ticket/tab/update_sort`, params)
// 删除tab项
export const deleteTicketTab: ApiT = params => FpRequest.post(`${API}/ticket/tab/del`, params)
// 删除tab项
export const getTicketCount: ApiT = params => FpRequest.post(`${API}/ticket/tab/count`, params)

// 金币查询
export const getCoinList: ApiT = params => FpRequest.post(`${API}/data_plat/user/gold_info`, params)
// 支付查询
export const getPayList: ApiT = params => FpRequest.post(`${API}/data_plat/user/pay_info`, params)
// 商品查询
export const getGoodsList: ApiT = params => FpRequest.post(`${API}/data_plat/user/item_info`, params)
// 登录查询
export const getLoginList: ApiT = params => FpRequest.post(`${API}/data_plat/user/login_info`, params)
// 获取商品列表下拉
export const getGoodsOpts: ApiT = params => FpRequest.post(`${API}/data_plat/game_item/opts`, params)

// 获取工单公共标签
export const getTicketCommonTag: ApiT = params => FpRequest.post(`${API}/ticket/tag/public`, params)
// 批量删除工单标签
export const batchDeleteTicketTag: ApiT = params => FpRequest.post(`${API}/ticket/tag/batch_delete`, params)
// 批量备注
export const batchRemark: ApiT = params => FpRequest.post(`${API}/ticket/batch_remark`, params)
// 获取游戏版本列表
export const getGameVersions: ApiT = params => FpRequest.get(`/gateway/api/app/version`, params)

