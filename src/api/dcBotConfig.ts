import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 获取DC机器人配置列表
export const getDCBotConfigList = (params: Record<string, unknown>) => FpRequest.post(`${API}/dsc_bot_config/list`, params)

// 验证DC机器人服务器
export const verifyDCBotServer = (params: Record<string, unknown>) => FpRequest.post(`${API}/dsc_bot_config/check`, params)

// 发布DC机器人配置
export const publishDCBotConfig = (params: Record<string, unknown>) => FpRequest.post(`${API}/dsc_bot_config/add`, params)

// 修改欢迎语
export const updateWelcomeMessage = (params: Record<string, unknown>) => FpRequest.post(`${API}/dsc_bot_config/update_welcome_message`, params)
