import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 获取训练结果列表
export const getTrainingResultsList: ApiT = params => FpRequest.post(`${API}/v2/question/trainlog`, params)
// 启动新的训练
export const startTraining: ApiT = params => FpRequest.post(`${API}/data_plat/training_results/start`, params)
// 部署模型
export const deployModel: ApiT = params => FpRequest.post(`${API}/data_plat/training_results/deploy`, params)
// 删除模型
export const deleteModel: ApiT = params => FpRequest.post(`${API}/data_plat/training_results/delete`, params)
// 获取模型详情
export const getModelDetail: ApiT = params => FpRequest.post(`${API}/data_plat/training_results/detail`, params)
