import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 质检任务列表
export const qcTaskList: ApiT = params => FpRequest.post(`${API}/examine/task/list`, params)
// 获取打分表
export const qcFormTpl: ApiT = params => FpRequest.post(`${API}/examine/tpl/opts`, params)
// 获取全检标准下的dc数据量
export const qcDcCount: ApiT = params => FpRequest.post(`${API}/examine/task/dsc/filter_count`, params)
// 获取部分Tab下的数据量
export const qcPartCount: ApiT = params => FpRequest.post(`${API}/examine/task/dsc/filter_part_count`, params)
// 获取客服专员列表
export const qcServiceList: ApiT = params => FpRequest.post(`/gateway/api/per/prd/gameUserList`, params)
// 保存质检任务
export const qcTaskSave: ApiT = params => FpRequest.post(`${API}/examine/task/save`, params)