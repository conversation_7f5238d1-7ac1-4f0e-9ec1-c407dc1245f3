import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// line玩家池列表
export const linePlayerPoll: ApiT = params => FpRequest.post(`${API}/line/user/pool`, params)
// line玩家查询结果下载接口
export const lineExportDown: ApiT = params => FpRequest.download(`${API}/line/user/pool/export`, 'post', params)
// line玩家数据概览接口
export const getStats: ApiT = params => FpRequest.post(`${API}/line/user/stats`, params)
// line发送消息接口
export const lineSendMsg: ApiT = params => FpRequest.post(`${API}/line/channel/send_message`, params)
// line历史聊天记录接口
export const lineMessageList: ApiT = params => FpRequest.post(`${API}/line/channel/dialogue`, params)
// line拉取新消息接口
export const lineNewMessage: ApiT = params => FpRequest.post(`${API}/line/channel/dialogue_refresh`, params)
// 获取玩家画像信息
export const linePortrait: ApiT = params => FpRequest.post(`${API}/line/portrait/info`, params)
// 新增/编辑 玩家画像标签信息
export const linePortraitTag: ApiT = params => FpRequest.post(`${API}/line/portrait/edit_tag`, params)
// 新增/编辑 玩家画像基础信息
export const linePortraitBasic: ApiT = params => FpRequest.post(`${API}/line/portrait/edit_basic`, params)
// 新增/编辑 玩家画像备注信息
export const linePortraitRemark: ApiT = params => FpRequest.post(`${API}/line/portrait/edit_remark`, params)
// 新增line沟通记录
export const lineAddCommunicate: ApiT = params => FpRequest.post(`${API}/line/commu/save`, params)
// Line频道列表（根据游戏变化）
export const lineChannelList: ApiT = params => FpRequest.post(`${API}/addons/line_channel_list `, params)
// 添加备注
export const lineAddMark: ApiT = params => FpRequest.post(`${API}/line/user/remark/save`, params)
// 添加Tab
export const addLineTab: ApiT = params => FpRequest.post(`${API}/line/tab/save`, params)
// 编辑Tab
export const editLineTab: ApiT = params => FpRequest.post(`${API}/line/tab/edit`, params)
// 删除tab项
export const delLineTab: ApiT = params => FpRequest.post(`${API}/line/tab/del`, params)
// 搜索tab列表
export const lineTabList: ApiT = params => FpRequest.post(`${API}/line/tab/list`, params)
// 设置搜索tab排序
export const updateTabSettingOrder: ApiT = params => FpRequest.post(`${API}/line/tab/update_sort`, params)
// 自定义tab数量
export const lineTabCount: ApiT = params => FpRequest.post(`${API}/line/tab/count`, params)
