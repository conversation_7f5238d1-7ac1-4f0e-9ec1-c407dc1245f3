import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 获取工单优质语料库列表
export const getTicketCorpusList: ApiT = params => FpRequest.post(`${API}/v2/question/list`, params)
// 添加/编辑工单优质语料
export const saveTicketCorpus: ApiT = params => FpRequest.post(`${API}/v2/question/save`, params)
// 删除工单优质语料
export const deleteTicketCorpus: ApiT = params => FpRequest.post(`${API}/v2/question/del`, params)
// 导入工单优质语料
export const importTicketCorpus: ApiT = params => FpRequest.post(`${API}/v2/question/batch_import`, params)
// 训练工单知识库
export const trainCorpus: ApiT = params => FpRequest.post(`${API}/v2/question/training`, params)
// 导出工单知识库
export const exportCorpus: ApiT = params => FpRequest.download(`${API}/v2/question/list/export`, 'post', params)
