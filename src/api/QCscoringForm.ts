import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// 质检打分表配置列表
export const qcScoringFormList: ApiT = params => FpRequest.post(`${API}/examine/tpl/list`, params)
// 质检打分表状态修改
export const qcScoringFormEnable: ApiT = params => FpRequest.post(`${API}/examine/tpl/enable`, params)
// 质检打分表保存
export const qcScoringFormSave: ApiT = params => FpRequest.post(`${API}/examine/tpl/save`, params)
// 质检打分表配置详情
export const qcScoringFormDetail: ApiT = params => FpRequest.post(`${API}/examine/tpl/detail`, params)
// 质检打分表配置删除
export const qcScoringFormDelete: ApiT = params => FpRequest.post(`${API}/examine/tpl/del`, params)
// 质检打分表配置复制
export const qcScoringFormCopy: ApiT = params => FpRequest.post(`${API}/examine/tpl/copy`, params)