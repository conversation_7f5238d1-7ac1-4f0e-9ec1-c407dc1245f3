/* 滚动条优化 start */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f6f6f6;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #cdcdcd;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #747474;
}

::-webkit-scrollbar-corner {
  background: #f6f6f6;
}
.el-table th.el-table__cell {
  background: rgb(250, 250, 250) !important;
}
.fptable {
  .el-table__fixed-right-patch {
    background: rgb(250, 250, 250);
    height: 37px !important;
  }
  
  .el-table__fixed-right-patch {
    border-bottom: 0px;
  }
}
/* 滚动条优化 end */