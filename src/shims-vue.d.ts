/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
interface ApiT {
	(params: Record<string, unknown>): any
}
interface TableResponse {
  data: {
    records: Record<string, unknown>[], // 表格数据
  } | Record<string, unknown>[]
  total: number
  current_page: number
}
interface Column {
  prop: string
  label: string
  width?: string
  align?: string
}
interface CompPromise {
  (): Promise<typeof import("*.vue")>
}