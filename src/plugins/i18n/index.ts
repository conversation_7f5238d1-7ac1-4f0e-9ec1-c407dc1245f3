/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2024-04-16 11:01:25 
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-04-30 10:42:23
 */
import { createI18n } from 'vue-i18n'
import langConf from '@/assets/lang/index'


const currentLang = navigator.language.replace(/-(\S*)/, '') === 'zh' ? 'zh' : 'en'
const zh = {
  ...langConf.config.zh
}
const en = {
  ...langConf.config.en
}
export default createI18n({
  locale: currentLang,
  fallbackLocale: 'en',
  legacy: false,
  globalInjection: true,
  messages: {
    zh,
    en
  }
})