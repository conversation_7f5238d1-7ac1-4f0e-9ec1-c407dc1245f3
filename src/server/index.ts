/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2024-04-16 20:00:47 
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-05-13 12:06:22
 */
import FpAxios from './interceptor'
import type { Method, AxiosPromise, AxiosRequestConfig } from 'axios'
import { useUserInfoStore } from '@/stores'
import { useLangsStore } from '@/stores'
import { useAppStore } from '@/stores/app'

/**
 *  Uniform interface request
 * @param url
 * @param method
 * @param options Header options
 * @param params Request information
 * @returns Promise
 */

type ResponseType = 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream'
class FpRequest extends FpAxios {
  private static http(url: string, method: Method = 'get', options: Record<string, unknown> = {}, params: Record<string, unknown> = {}, responseType?: ResponseType, normal?: boolean) {
    const { locale } = useLangsStore()
    const { userTocken } = useUserInfoStore()
    const lang = locale ? {
      zh: 'zh-cn',
      en: 'en',
      ja: 'ja',
      ko: 'ko'
    }[locale] : 'zh-cn'
    // header options
    const headers = normal ? {} : {
      lang,
      'admin-gateway-token': userTocken,
      ...options
    }
		let reqData = {}
		if (method === 'get') reqData = { params }
		if (method === 'post') reqData = { data: params }
    const serverConfig: AxiosRequestConfig = {
      url,
      method: method,
      ...reqData,
      headers
    }
    if (responseType) {
      serverConfig.responseType = responseType
    }
		return this.server(serverConfig)
  }
  public static get(url: string, params: Record<string, unknown> = {}, options: Record<string, unknown> = {}): AxiosPromise {
    return this.http(url, 'get', options, params)
  }

  public static post(url: string, params: Record<string, unknown> = {}, options: Record<string, unknown> = {}): AxiosPromise {
    return this.http(url, 'post', options, params)
  }

  public static download(url: string, method: Method = 'get', params: Record<string, unknown> = {}, normal: boolean = false, options: Record<string, unknown> = {}) {
    useAppStore().setStartTime(Date.now())
    useAppStore().Progress(0)
    this.http(url, method, options, params, 'arraybuffer', normal).then((res) => {
      const blob = new Blob([res.data], {
        type: res.headers['content-type']
      })
      if (res.headers['content-type'].includes('application/json')) {
        const reader = new FileReader()
        reader.readAsText(blob, 'utf-8')
        reader.onload = () => {
          const data: { msg: string } = JSON.parse(reader.result as string)
          ElMessage.error(data.msg)
        }
      } else {
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = params.filename ? params.filename as string : decodeURIComponent(res.headers['filename'])
        link.click()
      }
    }).finally(() => {
      useAppStore().Progress(100)
    }).catch((err) => {
      console.log(err)
      useAppStore().Progress(-1)
      ElMessage.error('下载失败！请重试')
    })
  }
}

export default FpRequest
