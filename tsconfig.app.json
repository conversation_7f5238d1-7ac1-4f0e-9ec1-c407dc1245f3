{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "*.ts", "src/**/*.ts", "src/**/**/*.ts", "src/**/**/**/*.ts", "src/**/*.vue", "src/**/**/*.vue", "src/**/**/**/*.vue", "src/**/**/**/*.json"], "exclude": ["src/**/__tests__/*", "node_modules"], "compilerOptions": {"composite": true, "allowJs": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}