const fs = require('fs');
const path = require('path');
const xlsx = require('node-xlsx')

// 从命令行参数获取game参数
const getParams = (argv) => {
  const t = new RegExp(/^--/g);
  const params = {};
  argv.filter(item => {
    return item.match(t);
  }).forEach(item => {
    const r = item.replace('--', '').split('=');
    params[r[0]] = r[1];
  });
  return params;
};


async function main() {
  try {
    const params = getParams(process.argv)
    const data = xlsx.parse('./客服系统后台多语言.xlsx')
    const table = (data[0] || [{}]).data || []

    if (!table || table.length === 0) {
      throw new Error('表格数据为空');
    }

    console.log('获取到的表格数据行数:', table.length);
    console.log('第一行数据:', table[0]);

    // 遍历lang key
    for (let j = 2; j < table[0].length; j++) {
      let langCode = table[0][j];
      let obj = '';

      // 遍历每一行内容
      for (let i = 1; i < table.length; i++) {
        // 判断key是否为空
        if (table[i][0] && table[i][0] !== '') {
          if (table[i][j]) {
            obj += ',\n"' + table[i][0].replace('"', '') + '":"' + table[i][j].replace(/["]+/gm, '').replace(/\r|\n/g, '<br/>') + '"';
          }
        }
      }

      let result = '{\n' + obj.substr(2) + '\n}';
      const outputDir = path.resolve('../src/assets/lang' + (params.game ? `/${params.game}` : ''));

      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // 写入语言文件
      const filePath = path.join(outputDir, langCode + '.json');
      fs.writeFile(filePath, result, function (err) {
        if (err) {
          console.log('Error! ' + err);
          return;
        }
        console.log(`${langCode}.json 写入完成`);
      });
    }
  } catch (error) {
    console.error('处理失败:', error);
    process.exit(1);
  }
}

main();
